package com.example.mcp_microservice_chatboot_ai.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for API URLs
 * Centralizes all external API base URLs for better configuration management
 * Removes hardcoded localhost URLs and makes them configurable via application.properties
 */
@Configuration
public class ApiUrlsConfig {

    @Value("${chat-mcp.api.base-url}")
    private String chatMcpBaseUrl;

    @Value("${win-mcp.api.base-url}")
    private String winMcpBaseUrl;

    /**
     * Get the base URL for Chat-MCP API
     * @return Chat-MCP base URL from application.properties
     */
    public String getChatMcpBaseUrl() {
        return chatMcpBaseUrl;
    }

    /**
     * Get the base URL for Win-MCP API
     * @return Win-MCP base URL from application.properties
     */
    public String getWinMcpBaseUrl() {
        return winMcpBaseUrl;
    }
}
