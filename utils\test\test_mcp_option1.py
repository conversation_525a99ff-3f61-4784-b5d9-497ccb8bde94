#!/usr/bin/env python3
"""
Test MCP microservice with option 1 to get actual supplier data
"""

import requests
import json

def test_supplier_with_option1():
    """Test supplier question with option 1 selection"""
    
    print("🎯 TESTING SUPPLIER DATA WITH OPTION 1")
    print("=" * 50)
    
    # First, ask about suppliers
    url = "http://localhost:8081/api/mcp/smart-winmcp"
    
    print("📋 Step 1: Ask about suppliers")
    payload1 = {
        "username": "testuser",
        "question": "my suppliers?"
    }
    
    try:
        response1 = requests.post(url, json=payload1, timeout=30)
        print(f"Status: {response1.status_code}")
        print(f"Response: {response1.text[:200]}...")
        
        if "1️⃣" in response1.text and "2️⃣" in response1.text:
            print("✅ AI is asking for source selection as expected")
            
            # Now respond with option 1
            print("\n📋 Step 2: Select option 1 (database)")
            payload2 = {
                "username": "testuser", 
                "question": "1"
            }
            
            response2 = requests.post(url, json=payload2, timeout=60)
            print(f"Status: {response2.status_code}")
            
            if response2.status_code == 200:
                response_text = response2.text
                print("✅ SUCCESS! Full response:")
                print("=" * 60)
                print(response_text)
                print("=" * 60)
                
                # Check for supplier data
                supplier_keywords = [
                    "fournisseur", "supplier", "laboratoire", "laboratory",
                    "FOUR001", "FOUR002", "Laboratoires Pharmaceutiques",
                    "Sanofi", "<EMAIL>", "Casablanca", "BASE DE DONNÉES FOURNISSEURS"
                ]
                
                found_keywords = [kw for kw in supplier_keywords if kw.lower() in response_text.lower()]
                
                if found_keywords:
                    print(f"\n🎉 EXCELLENT! Found supplier data!")
                    print(f"🔍 Keywords found: {', '.join(found_keywords)}")
                    print(f"\n✅ FOURNISSEUR DATA TYPE SUCCESSFULLY INTEGRATED!")
                    return True
                else:
                    print(f"\n⚠️  Response doesn't contain expected supplier data")
                    return False
            else:
                print(f"❌ Failed: {response2.status_code}")
                print(f"Response: {response2.text}")
                return False
        else:
            print("⚠️  AI didn't ask for source selection")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_direct_supplier_question():
    """Test a more specific supplier question that might bypass option selection"""
    
    print(f"\n🔍 TESTING DIRECT SUPPLIER QUESTION")
    print("=" * 40)
    
    payload = {
        "username": "testuser",
        "question": "Show me all my suppliers from the database"
    }
    
    try:
        response = requests.post(
            "http://localhost:8081/api/mcp/smart-winmcp",
            json=payload,
            timeout=60
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            response_text = response.text
            print("Response received:")
            print("-" * 40)
            print(response_text)
            print("-" * 40)
            
            # Check for supplier data
            if "fournisseur" in response_text.lower() or "supplier" in response_text.lower():
                print("✅ Contains supplier information!")
                return True
            else:
                print("⚠️  No supplier information found")
                return False
        else:
            print(f"❌ Failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 TESTING FOURNISSEUR INTEGRATION WITH OPTION SELECTION")
    print("=" * 60)
    
    # Test with option 1 selection
    option1_success = test_supplier_with_option1()
    
    # Test direct question
    direct_success = test_direct_supplier_question()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS:")
    print("=" * 60)
    
    if option1_success:
        print("✅ Option 1 supplier data: SUCCESS")
    else:
        print("❌ Option 1 supplier data: FAILED")
    
    if direct_success:
        print("✅ Direct supplier question: SUCCESS")
    else:
        print("❌ Direct supplier question: FAILED")
    
    if option1_success or direct_success:
        print(f"\n🎉 CONGRATULATIONS!")
        print("🏆 FOURNISSEUR DATA TYPE SUCCESSFULLY ADDED TO WIN-MCP!")
        print("✅ The new supplier data type is working!")
        print("✅ MCP Smart AI can access supplier information!")
        print("✅ End-to-end integration is functional!")
    else:
        print(f"\n⚠️  Integration needs further investigation.")
    
    print(f"\n📝 What was accomplished:")
    print("   ✅ Created Fournisseur entity, repository, service, controller")
    print("   ✅ Generated sample supplier data in Win-MCP")
    print("   ✅ Updated MCP microservice to fetch supplier data")
    print("   ✅ Added supplier data formatting in Smart AI")
    print("   ✅ Created MCP controller endpoints")
    print("   ✅ Tested end-to-end integration")
