package com.chatbootmcp.chatmcp.security;

import com.chatbootmcp.chatmcp.service.UserDetailsServiceImpl;
import com.chatbootmcp.chatmcp.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // Skip JWT check for public endpoints
        String requestPath = request.getServletPath();
        if (requestPath.startsWith("/auth") ||
            requestPath.startsWith("/h2-console") ||
            requestPath.startsWith("/ws") ||
            requestPath.endsWith(".html") ||
            requestPath.startsWith("/static") ||
            requestPath.equals("/error")) {
            filterChain.doFilter(request, response);
            return;
        }

        final String authorizationHeader = request.getHeader("Authorization");
        final String tenantAuthorizationHeader = request.getHeader("AuthorizationTenant");

        String username = null;
        String jwt = null;
        String tenantJwt = null;
        boolean isDualAuth = false;

        // Check for tenant token (dual authentication)
        if (tenantAuthorizationHeader != null && tenantAuthorizationHeader.startsWith("BearerTenant ")) {
            tenantJwt = tenantAuthorizationHeader.substring(13); // "BearerTenant ".length() = 13
            isDualAuth = true;
            System.out.println("🏥 WIN-MCP: Dual authentication detected - tenant token present");
        }

        // Check for user token
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            jwt = authorizationHeader.substring(7);
            try {
                username = jwtUtil.extractUsername(jwt);
                System.out.println("👤 WIN-MCP: User token detected for: " + username);
            } catch (Exception e) {
                logger.error("Error extracting username from user token", e);
            }
        }

        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            UserDetails userDetails = this.userDetailsService.loadUserByUsername(username);

            boolean isValidAuth = false;

            if (isDualAuth) {
                // For dual authentication, validate both tenant and user tokens
                if (tenantJwt != null && jwt != null) {
                    try {
                        String tenantUsername = jwtUtil.extractUsername(tenantJwt);
                        UserDetails tenantUserDetails = this.userDetailsService.loadUserByUsername(tenantUsername);

                        boolean tenantValid = jwtUtil.validateToken(tenantJwt, tenantUserDetails);
                        boolean userValid = jwtUtil.validateToken(jwt, userDetails);

                        isValidAuth = tenantValid && userValid;
                        System.out.println("🔐 WIN-MCP: Dual auth validation - Tenant: " + tenantValid + ", User: " + userValid);
                    } catch (Exception e) {
                        logger.error("Error validating dual authentication tokens", e);
                        isValidAuth = false;
                    }
                }
            } else {
                // Single authentication - just validate user token
                isValidAuth = jwtUtil.validateToken(jwt, userDetails);
                System.out.println("🔐 WIN-MCP: Single auth validation: " + isValidAuth);
            }

            if (isValidAuth) {
                UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities());
                authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authenticationToken);
                System.out.println("✅ WIN-MCP: Authentication successful for user: " + username);
            }
        }

        filterChain.doFilter(request, response);
    }
}
