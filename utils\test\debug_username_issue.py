#!/usr/bin/env python3
"""
Debug the username issue in MCP microservice
"""

import requests
import json

def test_mcp_microservice_username():
    """Test if MCP microservice receives the correct username"""
    print("🔍 DEBUGGING USERNAME ISSUE IN MCP MICROSERVICE")
    print("=" * 60)
    
    # Test different usernames to see what happens
    test_cases = [
        ("user1", "Testing with user1"),
        ("user2", "Testing with user2"),
        ("", "Testing with empty username"),
        ("invalid_user", "Testing with invalid username")
    ]
    
    for username, description in test_cases:
        print(f"\n🧪 {description}: '{username}'")
        
        try:
            # Send a medical question to MCP microservice
            response = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": f"debug-{username}",
                    "content": "quel est mon groupe sanguin?",
                    "username": username
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                content = data.get('content', '')
                print(f"   💬 Response: {content[:100]}...")
                
                # If it asks for source selection, choose option 1 (database)
                if "Souhaitez-vous que je vous réponde" in content:
                    print(f"   🔄 Selecting database option...")
                    
                    # Send option 1
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": f"debug-{username}",
                            "content": "1",
                            "username": username
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   ✅ Final Response: {content2[:200]}...")
                        
                        # Analyze the response
                        if "o+" in content2.lower() or "groupe sanguin" in content2.lower():
                            print(f"   🎉 SUCCESS: Got medical data for {username}!")
                        elif "je n'ai pas trouvé" in content2.lower() or "information n'est pas disponible" in content2.lower():
                            print(f"   ❌ FAILED: No data found for {username}")
                        elif "erreur d'authentification" in content2.lower():
                            print(f"   ❌ AUTH ERROR: Authentication failed for {username}")
                        else:
                            print(f"   ⚠️  UNCLEAR: Unclear response for {username}")
                    else:
                        print(f"   ❌ Option selection failed: {response2.text}")
                        
            else:
                print(f"   ❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_backend_authentication_directly():
    """Test backend authentication directly to verify usernames work"""
    print(f"\n🔐 TESTING BACKEND AUTHENTICATION DIRECTLY")
    print("=" * 50)
    
    # Test Chat-MCP authentication
    print("\n1. Testing Chat-MCP Authentication:")
    for username in ["user1", "user2"]:
        try:
            response = requests.post(
                "http://localhost:8080/api/auth/login",
                json={"username": username, "password": "password"},
                headers={'Content-Type': 'application/json'}
            )
            print(f"   {username}: {response.status_code} {'✅ SUCCESS' if response.status_code == 200 else '❌ FAILED'}")
            if response.status_code == 200:
                data = response.json()
                print(f"      Token: {data.get('token', 'N/A')[:20]}...")
                print(f"      Username: {data.get('username', 'N/A')}")
        except Exception as e:
            print(f"   {username}: ❌ ERROR - {e}")
    
    # Test Win-MCP authentication
    print("\n2. Testing Win-MCP Dual Authentication:")
    try:
        # Step 1: Tenant auth
        tenant_response = requests.post(
            "http://localhost:8082/auth/tenant/login",
            json={"username": "0001", "password": "123456"},
            headers={'Content-Type': 'application/json'}
        )
        print(f"   Tenant: {tenant_response.status_code} {'✅ SUCCESS' if tenant_response.status_code == 200 else '❌ FAILED'}")
        
        if tenant_response.status_code == 200:
            tenant_token = tenant_response.json().get('token')
            
            # Step 2: User auth for both users
            for username in ["user1", "user2"]:
                user_response = requests.post(
                    "http://localhost:8082/auth/login",
                    json={"username": username, "password": "password"},
                    headers={
                        'Content-Type': 'application/json',
                        'AuthorizationTenant': f'BearerTenant {tenant_token}'
                    }
                )
                print(f"   {username}: {user_response.status_code} {'✅ SUCCESS' if user_response.status_code == 200 else '❌ FAILED'}")
                if user_response.status_code == 200:
                    data = user_response.json()
                    print(f"      Token: {data.get('token', 'N/A')[:20]}...")
                    print(f"      Username: {data.get('username', 'N/A')}")
    except Exception as e:
        print(f"   Win-MCP Auth: ❌ ERROR - {e}")

def test_mcp_microservice_logs():
    """Check what the MCP microservice logs show"""
    print(f"\n📋 INSTRUCTIONS FOR CHECKING MCP MICROSERVICE LOGS")
    print("=" * 60)
    print("1. Check the MCP microservice terminal for DEBUG messages")
    print("2. Look for lines like:")
    print("   - 'DEBUG: Calling /api/users/data with token for user: XXX'")
    print("   - 'DEBUG: Received user data: XXX'")
    print("   - 'DEBUG: Formatted user data: XXX'")
    print("3. Check if the username is being passed correctly")
    print("4. Check if authentication is working")
    print("5. Check if data is being retrieved")

def main():
    print("🐛 DEBUGGING USERNAME ISSUE")
    print("=" * 70)
    
    # Test MCP microservice with different usernames
    test_mcp_microservice_username()
    
    # Test backend authentication directly
    test_backend_authentication_directly()
    
    # Instructions for checking logs
    test_mcp_microservice_logs()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 DEBUGGING SUMMARY")
    print(f"=" * 70)
    print(f"1. Test if MCP microservice receives correct username")
    print(f"2. Test if backend authentication works for user1/user2")
    print(f"3. Check MCP microservice logs for DEBUG messages")
    print(f"4. Verify data retrieval and formatting")
    print(f"\n💡 POSSIBLE ISSUES:")
    print(f"- Username not being passed from Angular frontend")
    print(f"- Authentication failing in MCP microservice")
    print(f"- Data not being retrieved from backend")
    print(f"- AI prompt not processing data correctly")

if __name__ == "__main__":
    main()
