package com.example.mcp_microservice_chatboot_ai.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for MCP chat responses compatible with Angular frontend.
 * This matches the McpChatResponse interface in Angular.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpChatResponseDto {
    
    /**
     * The unique identifier of the message.
     */
    private String id;
    
    /**
     * The ID of the conversation this message belongs to.
     */
    private String conversationId;
    
    /**
     * The role of the message sender (user, assistant, or system).
     */
    private String role;
    
    /**
     * The content of the message.
     */
    private String content;
    
    /**
     * The timestamp when the message was created (as string).
     */
    private String timestamp;
}
