#!/usr/bin/env python3
"""
Comprehensive test of all login credentials for both backends
"""

import requests
import json

def test_chat_mcp_login(username, password):
    """Test Chat-MCP single authentication"""
    print(f"🔑 Testing Chat-MCP login: {username}")
    
    url = "http://localhost:8080/api/auth/login"
    payload = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            token = data.get('token', '')
            print(f"   ✅ SUCCESS - Token: {token[:20]}...")
            return True
        else:
            print(f"   ❌ FAILED - Status: {response.status_code}, Response: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
        return False

def test_win_mcp_dual_login(username, password):
    """Test Win-MCP dual authentication"""
    print(f"🏥 Testing Win-MCP dual login: {username}")
    
    # Step 1: Tenant authentication
    tenant_url = "http://localhost:8082/auth/tenant/login"
    tenant_payload = {
        "username": "0001",
        "password": "123456"
    }
    
    try:
        tenant_response = requests.post(tenant_url, json=tenant_payload, headers={'Content-Type': 'application/json'})
        if tenant_response.status_code != 200:
            print(f"   ❌ TENANT AUTH FAILED - Status: {tenant_response.status_code}")
            return False
        
        tenant_data = tenant_response.json()
        tenant_token = tenant_data.get('token')
        print(f"   🏥 Tenant auth successful")
        
        # Step 2: User authentication with tenant token
        user_url = "http://localhost:8082/auth/login"
        user_payload = {
            "username": username,
            "password": password
        }
        user_headers = {
            'Content-Type': 'application/json',
            'AuthorizationTenant': f'BearerTenant {tenant_token}'
        }
        
        user_response = requests.post(user_url, json=user_payload, headers=user_headers)
        if user_response.status_code == 200:
            user_data = user_response.json()
            user_token = user_data.get('token', '')
            print(f"   ✅ SUCCESS - User Token: {user_token[:20]}...")
            return True
        else:
            print(f"   ❌ USER AUTH FAILED - Status: {user_response.status_code}, Response: {user_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
        return False

def test_win_mcp_medical_data(username, password):
    """Test Win-MCP medical data retrieval"""
    print(f"🩺 Testing Win-MCP medical data: {username}")
    
    # Get dual authentication tokens
    tenant_url = "http://localhost:8082/auth/tenant/login"
    tenant_payload = {
        "username": "0001",
        "password": "123456"
    }
    
    try:
        tenant_response = requests.post(tenant_url, json=tenant_payload, headers={'Content-Type': 'application/json'})
        if tenant_response.status_code != 200:
            print(f"   ❌ TENANT AUTH FAILED")
            return False
        
        tenant_data = tenant_response.json()
        tenant_token = tenant_data.get('token')
        
        user_url = "http://localhost:8082/auth/login"
        user_payload = {
            "username": username,
            "password": password
        }
        user_headers = {
            'Content-Type': 'application/json',
            'AuthorizationTenant': f'BearerTenant {tenant_token}'
        }
        
        user_response = requests.post(user_url, json=user_payload, headers=user_headers)
        if user_response.status_code != 200:
            print(f"   ❌ USER AUTH FAILED")
            return False
        
        user_data = user_response.json()
        user_token = user_data.get('token')
        
        # Test medical data API
        medical_url = f"http://localhost:8082/api/winplus/user-data/{username}"
        medical_headers = {
            'AuthorizationTenant': f'BearerTenant {tenant_token}',
            'Authorization': f'Bearer {user_token}'
        }
        
        medical_response = requests.get(medical_url, headers=medical_headers)
        if medical_response.status_code == 200:
            medical_data = medical_response.json()
            if medical_data.get('medicalProfile'):
                print(f"   ✅ MEDICAL DATA FOUND - Blood Type: {medical_data['medicalProfile'].get('bloodType', 'N/A')}")
                return True
            else:
                print(f"   ⚠️  NO MEDICAL DATA - User exists but no medical profile")
                return False
        else:
            print(f"   ❌ MEDICAL DATA FAILED - Status: {medical_response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR - {e}")
        return False

def main():
    print("🔐 COMPREHENSIVE LOGIN CREDENTIALS TEST")
    print("=" * 60)
    
    # Test credentials
    test_users = [
        ("user1", "password"),
        ("user2", "password"),
        ("user3", "password"),
        ("user4", "password"),
        ("admin", "password")
    ]
    
    chat_mcp_results = []
    win_mcp_results = []
    medical_data_results = []
    
    print("\n📋 TESTING CHAT-MCP SINGLE AUTHENTICATION")
    print("-" * 50)
    for username, password in test_users:
        result = test_chat_mcp_login(username, password)
        chat_mcp_results.append((username, result))
    
    print("\n🏥 TESTING WIN-MCP DUAL AUTHENTICATION")
    print("-" * 50)
    for username, password in test_users:
        result = test_win_mcp_dual_login(username, password)
        win_mcp_results.append((username, result))
    
    print("\n🩺 TESTING WIN-MCP MEDICAL DATA ACCESS")
    print("-" * 50)
    for username, password in test_users:
        result = test_win_mcp_medical_data(username, password)
        medical_data_results.append((username, result))
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 COMPREHENSIVE TEST SUMMARY")
    print("=" * 60)
    
    print("\n🔑 Chat-MCP Single Authentication Results:")
    for username, result in chat_mcp_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {username}: {status}")
    
    print("\n🏥 Win-MCP Dual Authentication Results:")
    for username, result in win_mcp_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {username}: {status}")
    
    print("\n🩺 Win-MCP Medical Data Results:")
    for username, result in medical_data_results:
        status = "✅ HAS DATA" if result else "❌ NO DATA"
        print(f"   {username}: {status}")
    
    # Overall results
    chat_success = sum(1 for _, result in chat_mcp_results if result)
    win_success = sum(1 for _, result in win_mcp_results if result)
    medical_success = sum(1 for _, result in medical_data_results if result)
    
    print(f"\n📊 OVERALL RESULTS:")
    print(f"   Chat-MCP: {chat_success}/{len(test_users)} users can login")
    print(f"   Win-MCP: {win_success}/{len(test_users)} users can login")
    print(f"   Medical Data: {medical_success}/{len(test_users)} users have medical profiles")
    
    if chat_success == len(test_users) and win_success == len(test_users):
        print(f"\n🎉 ALL AUTHENTICATION TESTS PASSED!")
        print(f"✅ Both backends are working correctly with all test credentials")
    else:
        print(f"\n⚠️  SOME AUTHENTICATION TESTS FAILED!")
        print(f"❌ Please check the failed credentials above")

if __name__ == "__main__":
    main()
