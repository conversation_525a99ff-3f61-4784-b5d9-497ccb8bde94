#!/usr/bin/env python3
"""
Test Chat-MCP API directly to debug the data issue
"""

import requests
import json

def test_chat_mcp_authentication():
    """Test Chat-MCP authentication"""
    print("🔐 TESTING CHAT-MCP AUTHENTICATION")
    print("=" * 50)
    
    # Test authentication
    auth_url = "http://localhost:8080/api/auth/login"
    auth_payload = {
        "username": "user1",
        "password": "password"
    }
    
    try:
        auth_response = requests.post(auth_url, json=auth_payload, headers={'Content-Type': 'application/json'})
        print(f"Auth Status: {auth_response.status_code}")
        
        if auth_response.status_code == 200:
            auth_data = auth_response.json()
            token = auth_data.get('token')
            print(f"✅ Authentication successful! Token: {token[:20]}...")
            return token
        else:
            print(f"❌ Authentication failed: {auth_response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def test_chat_mcp_user_data(token):
    """Test Chat-MCP user data endpoint"""
    print(f"\n📊 TESTING CHAT-MCP USER DATA")
    print("=" * 40)
    
    if not token:
        print("❌ No token available")
        return
    
    # Test user data endpoint
    user_data_url = "http://localhost:8080/api/users/data"
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(user_data_url, headers=headers)
        print(f"User Data Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ User data retrieved successfully!")
            print(f"📋 Data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            # Show specific user data
            if isinstance(data, dict):
                if 'userProfile' in data:
                    profile = data['userProfile']
                    print(f"👤 User Profile: {profile.get('fullName')} ({profile.get('username')})")
                    print(f"📧 Email: {profile.get('email')}")
                
                if 'transactions' in data:
                    transactions = data['transactions']
                    print(f"💰 Transactions: {len(transactions) if transactions else 0}")
                
                if 'invoices' in data:
                    invoices = data['invoices']
                    print(f"📄 Invoices: {len(invoices) if invoices else 0}")
                
                if 'orders' in data:
                    orders = data['orders']
                    print(f"📦 Orders: {len(orders) if orders else 0}")
            
            return data
        else:
            print(f"❌ User data failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ User data error: {e}")
        return None

def test_chat_mcp_specific_endpoints(token):
    """Test specific Chat-MCP endpoints"""
    print(f"\n🔍 TESTING SPECIFIC CHAT-MCP ENDPOINTS")
    print("=" * 50)
    
    if not token:
        print("❌ No token available")
        return
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    endpoints = [
        ("/api/real-data/transactions?limit=5", "Transactions"),
        ("/api/real-data/invoices?limit=5", "Invoices"),
        ("/api/real-data/orders?limit=5", "Orders"),
        ("/api/users/profile", "User Profile")
    ]
    
    for endpoint, name in endpoints:
        print(f"\n🔍 Testing {name}: {endpoint}")
        try:
            response = requests.get(f"http://localhost:8080{endpoint}", headers=headers)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    print(f"   ✅ {name}: {len(data)} items")
                    if data:
                        first_item = data[0]
                        if isinstance(first_item, dict):
                            print(f"   📋 First item keys: {list(first_item.keys())}")
                elif isinstance(data, dict):
                    print(f"   ✅ {name}: {list(data.keys())}")
                else:
                    print(f"   ✅ {name}: {type(data)}")
            else:
                print(f"   ❌ {name} failed: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ {name} error: {e}")

def test_mcp_microservice_with_chat_mcp():
    """Test MCP microservice with Chat-MCP questions"""
    print(f"\n🤖 TESTING MCP MICROSERVICE WITH CHAT-MCP")
    print("=" * 50)
    
    questions = [
        "quel est mon email?",
        "quelles sont mes dernières transactions?",
        "quelles sont mes factures?",
        "quel est mon solde?",
        "quelles sont mes commandes?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n❓ Test {i}: {question}")
        
        try:
            # Send the question
            response = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": f"test-chat-mcp-{i}",
                    "content": question,
                    "username": "user1"
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                content = data.get('content', '')
                print(f"   💬 Response: {content[:150]}...")
                
                # If it asks for source selection, choose option 1 (database)
                if "Souhaitez-vous que je vous réponde" in content:
                    print(f"   🔄 Selecting database option...")
                    
                    # Send option 1
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": f"test-chat-mcp-{i}",
                            "content": "1",
                            "username": "user1"
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   ✅ Final Response: {content2[:200]}...")
                        
                        # Check if we got actual data
                        if any(keyword in content2.lower() for keyword in ['email', 'transaction', 'facture', 'solde', 'commande']):
                            print(f"   🎉 SUCCESS: Got real data!")
                        elif "je n'ai pas trouvé" in content2.lower():
                            print(f"   ❌ FAILED: Still getting 'info not found'")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                    else:
                        print(f"   ❌ Option selection failed: {response2.text}")
                        
            else:
                print(f"   ❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    # Test Chat-MCP authentication
    token = test_chat_mcp_authentication()
    
    # Test Chat-MCP user data
    user_data = test_chat_mcp_user_data(token)
    
    # Test specific endpoints
    test_chat_mcp_specific_endpoints(token)
    
    # Test MCP microservice
    test_mcp_microservice_with_chat_mcp()
    
    print(f"\n" + "=" * 60)
    print(f"🎯 CHAT-MCP DEBUGGING SUMMARY")
    print(f"=" * 60)
    print(f"✅ Authentication: {'Working' if token else 'Failed'}")
    print(f"✅ User Data: {'Working' if user_data else 'Failed'}")
    print(f"🎯 Check if MCP microservice gets real data from Chat-MCP")

if __name__ == "__main__":
    main()
