.app-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;

  h1 {
    margin: 0;
    color: #333;
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: 20px;

    .mode-selector {
      display: flex;
      align-items: center;

      span {
        margin-right: 10px;
        font-weight: 500;
      }

      select {
        padding: 8px 12px;
        border: 1px solid #ccc;
        border-radius: 4px;
        background-color: white;
        font-size: 14px;

        &:focus {
          outline: none;
          border-color: #0078d4;
        }
      }
    }

    .logout-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;

      .user-info {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 13px;

        i {
          color: #0078d4;
          font-size: 18px;
        }

        .username {
          font-weight: 600;
          color: #333;
        }

        .backend-badge {
          background-color: #0078d4;
          color: white;
          padding: 2px 6px;
          border-radius: 10px;
          font-size: 10px;
          font-weight: 500;
          text-transform: uppercase;
        }
      }

      .logout-btn {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 6px 12px;
        background-color: #dc3545;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background-color: #c82333;
          transform: translateY(-1px);
        }

        i {
          font-size: 14px;
        }
      }
    }
  }
}

.content {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;

  p {
    line-height: 1.6;
    color: #333;
  }

  ul {
    padding-left: 20px;

    li {
      margin-bottom: 10px;
      line-height: 1.6;
    }
  }

  strong {
    color: #0078d4;
  }
}