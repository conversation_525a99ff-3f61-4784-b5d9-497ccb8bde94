#!/usr/bin/env python3
"""
Test complete medical profile flow with dual authentication
"""

import requests
import json
import time

def test_complete_medical_flow():
    """Test the complete medical profile flow"""
    print("🏥 Testing complete medical profile flow with dual authentication...")
    
    url = "http://localhost:8081/api/chat"
    
    # Step 1: Ask medical question
    print("\n📋 Step 1: Asking medical question...")
    request1 = {
        "conversationId": "test-complete-flow",
        "content": "quelles sont mes informations médicales personnelles?",
        "username": "user1"
    }
    
    response1 = requests.post(url, json=request1, headers={'Content-Type': 'application/json'})
    print(f"Status Code: {response1.status_code}")
    print(f"Response: {response1.text}")
    
    if response1.status_code != 200:
        print("❌ Failed to ask medical question")
        return False
    
    # Wait a moment for processing
    time.sleep(2)
    
    # Step 2: Respond with "1" to select database source
    print("\n🔢 Step 2: Selecting database source (option 1)...")
    request2 = {
        "conversationId": "test-complete-flow",
        "content": "1",
        "username": "user1"
    }
    
    response2 = requests.post(url, json=request2, headers={'Content-Type': 'application/json'})
    print(f"Status Code: {response2.status_code}")
    print(f"Response: {response2.text}")
    
    if response2.status_code == 200:
        response_data = response2.json()
        content = response_data.get('content', '')
        
        # Check if we got medical profile data
        if any(keyword in content.lower() for keyword in ['groupe sanguin', 'allergies', 'médicaments', 'médecin']):
            print("✅ Complete medical profile flow successful!")
            print("🎯 Medical profile data retrieved successfully!")
            return True
        else:
            print("⚠️  Response received but no medical data found")
            return False
    else:
        print("❌ Failed to get medical profile data")
        return False

def main():
    print("🔐 Testing Complete Medical Profile Flow with Dual Authentication")
    print("=" * 70)
    
    success = test_complete_medical_flow()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 COMPLETE FLOW TEST PASSED!")
        print("✅ Dual authentication system working correctly")
        print("✅ Medical profile data retrieval working correctly")
        print("✅ French language responses working correctly")
    else:
        print("❌ COMPLETE FLOW TEST FAILED!")
        print("Please check the logs above for details")

if __name__ == "__main__":
    main()
