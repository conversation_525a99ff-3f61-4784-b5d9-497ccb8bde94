#!/usr/bin/env python3
"""
Test Win-MCP backend data directly
"""

import requests
import json

def test_win_mcp_authentication():
    """Test Win-MCP authentication and data retrieval"""
    print("🔍 TESTING WIN-MCP BACKEND DATA")
    print("=" * 50)
    
    try:
        # Step 1: Tenant authentication
        print("📤 Step 1: Tenant authentication...")
        tenant_response = requests.post(
            "http://localhost:8082/auth/tenant/login",
            json={"username": "0001", "password": "123456"},
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Tenant auth status: {tenant_response.status_code}")
        if tenant_response.status_code != 200:
            print(f"   ❌ Tenant auth failed: {tenant_response.text}")
            return
        
        tenant_token = tenant_response.json().get('token')
        print(f"   ✅ Tenant token: {tenant_token[:20]}...")
        
        # Step 2: User authentication
        print(f"\n📤 Step 2: User authentication...")
        user_response = requests.post(
            "http://localhost:8082/auth/login",
            json={"username": "user1", "password": "password"},
            headers={
                'Content-Type': 'application/json',
                'AuthorizationTenant': f'BearerTenant {tenant_token}'
            }
        )
        
        print(f"   User auth status: {user_response.status_code}")
        if user_response.status_code != 200:
            print(f"   ❌ User auth failed: {user_response.text}")
            return
        
        user_token = user_response.json().get('token')
        print(f"   ✅ User token: {user_token[:20]}...")
        
        # Step 3: Test data endpoints
        print(f"\n📤 Step 3: Testing data endpoints...")
        
        headers = {
            'AuthorizationTenant': f'BearerTenant {tenant_token}',
            'Authorization': f'Bearer {user_token}'
        }
        
        # Test medical profile
        print(f"\n   🩺 Testing medical profile...")
        medical_response = requests.get(
            "http://localhost:8082/api/users/medical-profile",
            headers=headers
        )
        print(f"   Medical profile status: {medical_response.status_code}")
        if medical_response.status_code == 200:
            medical_data = medical_response.json()
            print(f"   ✅ Medical data: {json.dumps(medical_data, indent=2)}")
        else:
            print(f"   ❌ Medical profile failed: {medical_response.text}")
        
        # Test dashboard data
        print(f"\n   📊 Testing dashboard data...")
        dashboard_response = requests.get(
            "http://localhost:8082/api/users/dashboard",
            headers=headers
        )
        print(f"   Dashboard status: {dashboard_response.status_code}")
        if dashboard_response.status_code == 200:
            dashboard_data = dashboard_response.json()
            print(f"   ✅ Dashboard data: {json.dumps(dashboard_data, indent=2)}")
        else:
            print(f"   ❌ Dashboard failed: {dashboard_response.text}")
        
        # Test user data
        print(f"\n   👤 Testing user data...")
        user_data_response = requests.get(
            "http://localhost:8082/api/users/data",
            headers=headers
        )
        print(f"   User data status: {user_data_response.status_code}")
        if user_data_response.status_code == 200:
            user_data = user_data_response.json()
            print(f"   ✅ User data: {json.dumps(user_data, indent=2)}")
        else:
            print(f"   ❌ User data failed: {user_data_response.text}")
        
        # Test sales data
        print(f"\n   💰 Testing sales data...")
        sales_response = requests.get(
            "http://localhost:8082/api/sales",
            headers=headers
        )
        print(f"   Sales status: {sales_response.status_code}")
        if sales_response.status_code == 200:
            sales_data = sales_response.json()
            print(f"   ✅ Sales data: {json.dumps(sales_data, indent=2)}")
        else:
            print(f"   ❌ Sales failed: {sales_response.text}")
        
        # Test products data
        print(f"\n   📦 Testing products data...")
        products_response = requests.get(
            "http://localhost:8082/api/products",
            headers=headers
        )
        print(f"   Products status: {products_response.status_code}")
        if products_response.status_code == 200:
            products_data = products_response.json()
            print(f"   ✅ Products data: {json.dumps(products_data, indent=2)}")
        else:
            print(f"   ❌ Products failed: {products_response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def test_chat_mcp_data():
    """Test Chat-MCP backend data"""
    print(f"\n🔍 TESTING CHAT-MCP BACKEND DATA")
    print("=" * 50)
    
    try:
        # Step 1: Authentication
        print("📤 Step 1: Chat-MCP authentication...")
        auth_response = requests.post(
            "http://localhost:8080/api/auth/login",
            json={"username": "user1", "password": "password"},
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Auth status: {auth_response.status_code}")
        if auth_response.status_code != 200:
            print(f"   ❌ Auth failed: {auth_response.text}")
            return
        
        token = auth_response.json().get('token')
        print(f"   ✅ Token: {token[:20]}...")
        
        # Step 2: Test data endpoints
        headers = {'Authorization': f'Bearer {token}'}
        
        # Test user data
        print(f"\n   👤 Testing user data...")
        user_response = requests.get(
            "http://localhost:8080/api/users/data",
            headers=headers
        )
        print(f"   User data status: {user_response.status_code}")
        if user_response.status_code == 200:
            user_data = user_response.json()
            print(f"   ✅ User data: {json.dumps(user_data, indent=2)}")
        else:
            print(f"   ❌ User data failed: {user_response.text}")
        
        # Test transactions
        print(f"\n   💳 Testing transactions...")
        transactions_response = requests.get(
            "http://localhost:8080/api/transactions",
            headers=headers
        )
        print(f"   Transactions status: {transactions_response.status_code}")
        if transactions_response.status_code == 200:
            transactions_data = transactions_response.json()
            print(f"   ✅ Transactions data: {json.dumps(transactions_data, indent=2)}")
        else:
            print(f"   ❌ Transactions failed: {transactions_response.text}")
        
        # Test invoices
        print(f"\n   📄 Testing invoices...")
        invoices_response = requests.get(
            "http://localhost:8080/api/invoices",
            headers=headers
        )
        print(f"   Invoices status: {invoices_response.status_code}")
        if invoices_response.status_code == 200:
            invoices_data = invoices_response.json()
            print(f"   ✅ Invoices data: {json.dumps(invoices_data, indent=2)}")
        else:
            print(f"   ❌ Invoices failed: {invoices_response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

def main():
    print("🔍 BACKEND DATA INVESTIGATION")
    print("=" * 70)
    
    # Test Win-MCP data
    test_win_mcp_authentication()
    
    # Test Chat-MCP data
    test_chat_mcp_data()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 DATA INVESTIGATION SUMMARY")
    print(f"=" * 70)
    print(f"This will show us:")
    print(f"1. ✅ If Win-MCP backend has medical data")
    print(f"2. ✅ If Chat-MCP backend has user/transaction data")
    print(f"3. ✅ Which endpoints are working")
    print(f"4. ✅ What data is actually available")
    print(f"\n💡 EXPECTED FINDINGS:")
    print(f"- Win-MCP should have medical profiles, dashboard data")
    print(f"- Chat-MCP should have user data, transactions, invoices")
    print(f"- If data is missing, that explains the 'info not found' responses")

if __name__ == "__main__":
    main()
