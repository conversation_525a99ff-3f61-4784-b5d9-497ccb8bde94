@import "../../../assets/scss/custom/plugins/icons/materialdesignicons";

// Professional Design System Variables (Identical to Direct OpenAI)
$primary-color: #667eea;
$primary-light: #f7fafc;
$primary-dark: #5a67d8;
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$secondary-color: #f8fafc;
$accent-color: #4fd1c7;
$text-color: #2d3748;
$text-light: #718096;
$text-muted: #a0aec0;
$border-color: #e2e8f0;
$background-color: #ffffff;
$surface-color: #f7fafc;
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
$success-color: #48bb78;
$warning-color: #ed8936;
$danger-color: #f56565;
$info-color: #4299e1;
$font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;

// Spacing System
$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 1rem;      // 16px
$spacing-lg: 1.5rem;    // 24px
$spacing-xl: 2rem;      // 32px
$spacing-2xl: 3rem;     // 48px

// Border Radius
$radius-sm: 0.375rem;   // 6px
$radius-md: 0.5rem;     // 8px
$radius-lg: 0.75rem;    // 12px
$radius-xl: 1rem;       // 16px
$radius-2xl: 1.5rem;    // 24px

// Breakpoints
$mobile-breakpoint: 480px;
$tablet-breakpoint: 768px;

.chat-widget {
  position: fixed;
  bottom: $spacing-xl;
  right: $spacing-lg;
  z-index: 1000;
  font-family: $font-family;

  @media (max-width: $mobile-breakpoint) {
    bottom: $spacing-md;
    right: $spacing-md;
  }
}

// Original Chat Button (collapsed state) - Restored as requested
.chat-button {
  width: 50px; // Reduced width for icon-only view
  padding: 0;
  height: 50px;
  border-radius: 30px;
  background-color: #f0f7f9;
  color: #276578;
  border: 2px solid #276578;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: $shadow-xl;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative; // Added for better positioning

  @media (max-width: $mobile-breakpoint) {
    height: 45px;
  }

  &:hover {
    width: 180px; // Expand to full width on hover
    padding: 0 20px;
    justify-content: flex-start; // Changed to flex-start for better alignment

    svg {
      margin-right: 10px; // Add space between icon and text
    }

    .chat-button-text {
      opacity: 1;
      transform: translateX(0);
      visibility: visible;
      margin-left: 5px; // Add some space after the icon
    }
  }

  .chat-icon {
    font-size: 24px;

    @media (max-width: $mobile-breakpoint) {
      font-size: 20px;
    }
  }

  .chat-button-text {
    font-size: 16px;
    font-weight: 500;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    visibility: hidden;
    white-space: nowrap;
    position: absolute; // Position it absolutely
    left: 60px; // Position it to the right of the icon

    @media (max-width: $mobile-breakpoint) {
      font-size: 14px;
    }
  }

  svg {
    width: 35px;
    height: 35px;
    color: $text-light;
    transition: all 0.2s ease;
    visibility: visible !important; // Always visible
    opacity: 1 !important; // Always visible
    z-index: 1; // Ensure it's above other elements

    @media (max-width: $mobile-breakpoint) {
      width: 28px;
      height: 28px;
    }
  }
}

// Professional Chat Container (expanded state) - Identical to Direct OpenAI
.chat-container {
  width: 420px;
  height: 700px;
  background: $background-color;
  border-radius: $radius-2xl;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: $shadow-xl;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  // Glassmorphism effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
    z-index: 1;
  }

  @media (max-width: $tablet-breakpoint) {
    width: 90vw;
    max-width: 420px;
    height: 85vh;
    max-height: 700px;
  }

  @media (max-width: $mobile-breakpoint) {
    width: 100vw;
    height: 100vh;
    max-height: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    animation: mobileSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes mobileSlideUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Professional Chat Header - Identical to Direct OpenAI
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-lg $spacing-xl;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
  position: relative;
  z-index: 2;

  @media (max-width: $mobile-breakpoint) {
    padding: $spacing-md $spacing-lg;
  }

  .back-button {
    background: rgba(255, 255, 255, 0.8);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    cursor: pointer;
    color: $text-light;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: $shadow-sm;

    &:hover {
      background: $surface-color;
      color: $text-color;
      transform: translateX(-2px);
      box-shadow: $shadow-md;
    }

    &:active {
      transform: translateX(0);
    }
  }

  .header-title {
    font-weight: 700;
    font-size: 18px;
    color: $text-color;
    flex: 1;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 $spacing-md;
    letter-spacing: -0.025em;

    @media (max-width: $mobile-breakpoint) {
      font-size: 16px;
    }
  }

  // Professional Knowledge Source Toggle
  .knowledge-toggle {
    display: flex;
    align-items: center;
    margin: 0 $spacing-sm;

    .toggle-container {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      background: rgba(255, 255, 255, 0.8);
      padding: $spacing-xs $spacing-sm;
      border-radius: $radius-xl;
      backdrop-filter: blur(10px);
      box-shadow: $shadow-sm;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .toggle-label {
      font-size: 11px;
      font-weight: 600;
      color: $text-muted;
      transition: all 0.3s ease;
      white-space: nowrap;
      letter-spacing: 0.025em;

      &.active {
        color: $primary-color;
        font-weight: 700;
        font-size: 12px;
      }
    }

    .toggle-switch {
      position: relative;
      width: 40px;
      height: 20px;
      background: rgba(226, 232, 240, 0.5);
      border-radius: 10px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(226, 232, 240, 0.7);
        transform: scale(1.05);
      }

      .toggle-slider {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 16px;
        height: 16px;
        background: $primary-gradient;
        border-radius: 50%;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: $shadow-sm;

        &.internal {
          transform: translateX(20px);
        }
      }
    }

    @media (max-width: $mobile-breakpoint) {
      .toggle-container {
        padding: $spacing-xs;
        gap: $spacing-xs;
      }

      .toggle-switch {
        width: 36px;
        height: 18px;

        .toggle-slider {
          width: 14px;
          height: 14px;

          &.internal {
            transform: translateX(18px);
          }
        }
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: $spacing-sm;
  }

  .header-tabs {
    display: none; // Hidden for cleaner design
    gap: $spacing-xs;

    .tab {
      padding: $spacing-sm;
      width: 36px;
      height: 36px;
      cursor: pointer;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: $text-light;
      background: rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(10px);

      &:hover:not(.active) {
        background: $surface-color;
        color: $text-color;
        transform: translateY(-1px);
        box-shadow: $shadow-md;
      }

      &.active {
        background: $primary-gradient;
        color: white;
        box-shadow: $shadow-md;
      }
    }
  }

  .close-button {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: $shadow-sm;

    &:hover {
      transform: translateY(-1px) scale(1.05);
      box-shadow: $shadow-md;
      background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
    }

    &:active {
      transform: translateY(0) scale(1);
    }
  }
}

// Professional Chat Content - Identical to Direct OpenAI
.chat-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, $surface-color 0%, rgba(255, 255, 255, 0.95) 100%);
  z-index: 2;

  // Common styles for all tabs
  .home-tab, .chat-tab, .help-tab, .login-tab {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    background: transparent;
  }
}

// Professional Chat Tab - Identical to Direct OpenAI
.chat-tab {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;

  .conversation-list-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    padding: $spacing-lg;

    .new-chat-btn {
      padding: $spacing-md $spacing-lg;
      background: $primary-gradient;
      color: white;
      border: none;
      border-radius: $radius-xl;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-sm;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: $shadow-md;
      margin-bottom: $spacing-lg;
      letter-spacing: 0.025em;

      @media (max-width: $mobile-breakpoint) {
        padding: $spacing-md;
        margin-bottom: $spacing-md;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: $shadow-lg;
        background: linear-gradient(135deg, $primary-dark 0%, #6b46c1 100%);
      }

      &:active {
        transform: translateY(0);
      }

      .new-icon {
        font-size: 16px;
      }
    }

    .conversation-list {
      flex: 1;
      overflow-y: auto;
      padding: 0;

      // Custom scrollbar
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(102, 126, 234, 0.3);
        border-radius: 2px;
      }

      .conversation-item {
        padding: $spacing-md;
        margin-bottom: $spacing-sm;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: $radius-lg;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: $shadow-sm;

        @media (max-width: $mobile-breakpoint) {
          padding: $spacing-md;
        }

        &:hover {
          background: rgba(255, 255, 255, 0.9);
          transform: translateY(-2px);
          box-shadow: $shadow-md;
          border-color: rgba(102, 126, 234, 0.2);
        }

        &.active {
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
          border-color: rgba(102, 126, 234, 0.3);
          box-shadow: $shadow-md;

          .conv-title {
            color: $primary-color;
            font-weight: 600;
          }
        }

        .conv-info {
          display: flex;
          flex-direction: column;
          overflow: hidden;
          flex: 1;

          .conv-title {
            font-size: 14px;
            font-weight: 500;
            color: $text-color;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: $spacing-xs;
            letter-spacing: -0.025em;

            @media (max-width: $mobile-breakpoint) {
              font-size: 13px;
            }
          }

          .conv-date {
            font-size: 11px;
            color: $text-muted;
            font-weight: 500;

            @media (max-width: $mobile-breakpoint) {
              font-size: 10px;
            }
          }
        }

        .delete-btn {
          opacity: 0;
          background: rgba(245, 101, 101, 0.1);
          border: none;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: $danger-color;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          margin-left: $spacing-sm;

          &:hover {
            background: rgba(245, 101, 101, 0.2);
            transform: scale(1.1);
          }
        }

        &:hover .delete-btn {
          opacity: 1;
        }
      }
    }
  }

  .conversation-view {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

// Professional Footer Navigation - Identical to Direct OpenAI
.chat-footer {
  height: 70px;
  border-top: 1px solid rgba(226, 232, 240, 0.3);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20px);
  position: relative;
  z-index: 2;

  @media (max-width: $mobile-breakpoint) {
    height: 65px;
  }

  .footer-nav {
    display: flex;
    justify-content: space-around;
    height: 100%;
    padding: 0 $spacing-md;

    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      cursor: pointer;
      color: $text-muted;
      font-size: 11px;
      font-weight: 600;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: $radius-lg;
      margin: $spacing-xs;
      position: relative;
      letter-spacing: 0.025em;
      background: none;
      border: none;
      padding: $spacing-sm;

      @media (max-width: $mobile-breakpoint) {
        font-size: 10px;
        margin: calc($spacing-xs / 2);
        padding: $spacing-xs;
      }

      i {
        font-size: 22px;
        margin-bottom: $spacing-xs;
        transition: all 0.3s ease;

        @media (max-width: $mobile-breakpoint) {
          font-size: 20px;
          margin-bottom: 2px;
        }
      }

      span {
        transition: all 0.3s ease;
      }

      &:hover:not(.active) {
        color: $text-color;
        background: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);

        i {
          transform: scale(1.1);
        }
      }

      &:focus {
        outline: 2px solid $primary-color;
        outline-offset: 2px;
      }

      &.active {
        color: $primary-color;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);

        i {
          transform: scale(1.1);
        }

        // Active indicator
        &::before {
          content: '';
          position: absolute;
          top: -1px;
          left: 50%;
          transform: translateX(-50%);
          width: 20px;
          height: 3px;
          background: $primary-gradient;
          border-radius: 0 0 $radius-sm $radius-sm;
        }
      }
    }
  }
}

// Professional Error Message with Toast Style
.error-message {
  position: absolute;
  bottom: $spacing-lg;
  left: $spacing-lg;
  right: $spacing-lg;
  background: rgba(245, 101, 101, 0.1);
  backdrop-filter: blur(10px);
  color: $danger-color;
  padding: $spacing-md $spacing-lg;
  border-radius: $radius-lg;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
  animation: slideInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(245, 101, 101, 0.2);
  box-shadow: $shadow-md;

  .error-text {
    flex: 1;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
  }

  .close-error {
    background: none;
    border: none;
    color: $danger-color;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-left: $spacing-md;

    i {
      font-size: 16px;
    }

    &:hover {
      background: rgba(245, 101, 101, 0.2);
      transform: scale(1.1);
    }

    &:focus {
      outline: 2px solid $danger-color;
      outline-offset: 2px;
    }
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
