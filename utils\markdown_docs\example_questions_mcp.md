# 🤖 MCP System - Complete Example Questions Guide

This comprehensive guide contains all possible questions you can ask both **Chat-MCP** and **Win-MCP** systems to test the real database integration with user-specific data.

## 🏗️ System Architecture

- **Chat-MCP Backend:** Personal/Medical data (port 8080)
- **Win-MCP Backend:** Business/Pharmacy data (port 8082)
- **MCP Microservice:** Smart AI routing (port 8081)
- **Angular Frontend:** User interface (port 4200)

## 📋 How to Test

### 1. **Start All Services:**
```bash
# Terminal 1: Chat-MCP Backend
cd chat-mcp && .\mvnw.cmd spring-boot:run

# Terminal 2: Win-MCP Backend
cd win-mcp && .\mvnw.cmd spring-boot:run

# Terminal 3: MCP Microservice
cd mcp_microservice_chatboot_ai && .\mvnw.cmd spring-boot:run

# Terminal 4: Angular Frontend
cd angular-openai-chat-2 && npm start
```

### 2. **Test via API:**
```bash
# Chat-MCP Backend Test
curl -X POST http://localhost:8081/api/chat \
  -H "Content-Type: application/json" \
  -d '{"content": "Quel est mon nom?", "conversationId": "test123", "username": "user1", "backend": "CHAT_MCP"}'

# Win-MCP Backend Test
curl -X POST http://localhost:8081/api/chat \
  -H "Content-Type: application/json" \
  -d '{"content": "Mes clients?", "conversationId": "test456", "username": "user1", "backend": "WIN_MCP"}'
```

### 3. **Test via Angular Frontend:**
- Open http://localhost:4200
- Use the MCP chat widget
- Ask questions and select backend automatically

### 4. **Always choose option 1** for database questions to get real data from H2.

---

# 🏥 **CHAT-MCP QUESTIONS** (Personal & Medical Data)

## 👤 **PERSONAL INFORMATION QUESTIONS**

### Basic Identity
- `Quel est mon nom ?`
- `Quel est mon nom complet ?`
- `Comment je m'appelle ?`
- `Quelle est mon identité ?`
- `Qui suis-je ?`
- `My name?`
- `What is my full name?`

### Contact Information
- `Quelle est mon adresse email ?`
- `Quel est mon email ?`
- `Quelle est mon adresse ?`
- `Où j'habite ?`
- `Quel est mon numéro de téléphone ?`
- `Comment me contacter ?`
- `Dans quelle ville j'habite ?`
- `Quel est mon code postal ?`
- `Dans quel pays je vis ?`
- `My email address?`
- `Where do I live?`
- `My phone number?`

### Professional Information
- `Quel est mon métier ?`
- `Où je travaille ?`
- `Quelle est ma profession ?`
- `Dans quelle entreprise je travaille ?`
- `Quel est mon poste ?`
- `Quel est mon département ?`
- `Quel est mon ID employé ?`
- `Quelle est ma fonction ?`
- `What is my job?`
- `Where do I work?`

---

## 🏥 **MEDICAL INFORMATION QUESTIONS**

### Medical Profile
- `Quel est mon profil médical ?`
- `Mes informations médicales ?`
- `Mon dossier médical ?`
- `Quelle est ma taille ?`
- `Quel est mon poids ?`
- `Quel est mon groupe sanguin ?`
- `My medical profile?`
- `What is my height?`
- `What is my weight?`
- `My blood type?`

### Medical History
- `Quelles sont mes allergies ?`
- `Mes conditions chroniques ?`
- `Mes médicaments actuels ?`
- `Mon dernier check-up ?`
- `Mes notes médicales ?`
- `What are my allergies?`
- `My chronic conditions?`
- `Current medications?`
- `Last checkup date?`

### Emergency Information
- `Mon contact d'urgence ?`
- `Numéro d'urgence ?`
- `Mon médecin traitant ?`
- `Téléphone de mon médecin ?`
- `Mon numéro d'assurance ?`
- `Emergency contact?`
- `Doctor's name?`
- `Insurance number?`

## 💰 **FINANCIAL INFORMATION QUESTIONS**

### Account Balance
- `Quel est mon solde ?`
- `Quel est mon solde actuel ?`
- `Combien j'ai sur mon compte ?`
- `Quelle est ma balance ?`
- `Combien d'argent j'ai ?`
- `My balance?`
- `How much money do I have?`

### Credit and Limits
- `Quelle est ma limite de crédit ?`
- `Quel est mon plafond ?`
- `Combien je peux dépenser ?`
- `Quelle est ma limite ?`
- `My credit limit?`
- `How much can I spend?`

### Payment Information
- `Quelle est ma méthode de paiement ?`
- `Comment je paie ?`
- `Quels sont les derniers chiffres de ma carte ?`
- `Quand expire ma carte ?`
- `Quelle est la date d'expiration de ma carte ?`
- `Quand est ma prochaine échéance ?`
- `Quand dois-je payer ?`
- `My payment method?`
- `When does my card expire?`

---

## 📄 **SUBSCRIPTION INFORMATION QUESTIONS**

### Subscription Status
- `Quel est mon statut d'abonnement ?`
- `Mon abonnement est-il actif ?`
- `Suis-je abonné ?`
- `Quel type d'abonnement j'ai ?`
- `My subscription status?`
- `Is my subscription active?`

### Subscription Details
- `Quand a commencé mon abonnement ?`
- `Quand se termine mon abonnement ?`
- `Combien coûte mon abonnement ?`
- `Quel est le prix de mon abonnement ?`
- `Quand expire mon abonnement ?`
- `When did my subscription start?`
- `How much does my subscription cost?`

## 🧾 **TRANSACTION QUESTIONS**

### Recent Transactions
- `Quelles sont mes dernières transactions ?`
- `Mes transactions récentes ?`
- `Qu'est-ce que j'ai acheté récemment ?`
- `Mes derniers achats ?`
- `Historique de mes transactions ?`
- `My recent transactions?`
- `What did I buy recently?`
- `Transaction history?`

### Transaction Summary
- `Combien j'ai dépensé ?`
- `Total de mes débits ?`
- `Total de mes crédits ?`
- `Quel est mon solde net ?`
- `Balance de mes transactions ?`
- `How much did I spend?`
- `Total debits and credits?`

## 📋 **INVOICE QUESTIONS**

### Recent Invoices
- `Quelles sont mes factures ?`
- `Mes dernières factures ?`
- `Factures récentes ?`
- `Historique de mes factures ?`
- `Mes factures impayées ?`
- `My invoices?`
- `Recent invoices?`
- `Unpaid invoices?`

### Invoice Summary
- `Combien je dois ?`
- `Total de mes factures payées ?`
- `Total de mes factures impayées ?`
- `Combien de factures en retard ?`
- `Mes factures en souffrance ?`
- `How much do I owe?`
- `Total paid invoices?`
- `Overdue invoices?`

---

# 💊 **WIN-MCP QUESTIONS** (Business & Pharmacy Data)

## 🏢 **CLIENT INFORMATION QUESTIONS**

### Client Profile
- `Mes informations client ?`
- `Mon profil client ?`
- `Mes données client ?`
- `Qui suis-je en tant que client ?`
- `Mon compte client ?`
- `My client information?`
- `My client profile?`
- `Client account details?`

### Client Details
- `Mon nom de client ?`
- `Mon code client ?`
- `Mon adresse de facturation ?`
- `Mon email client ?`
- `Mon téléphone client ?`
- `My client code?`
- `My billing address?`
- `Client contact information?`

### Client Financial Status
- `Mon solde client ?`
- `Mon plafond de crédit ?`
- `Mon chiffre d'affaires ?`
- `Mon taux de remise ?`
- `Mon statut client ?`
- `My client balance?`
- `My credit limit?`
- `My discount rate?`

## 💰 **SALES INFORMATION QUESTIONS**

### Sales Overview
- `Mes ventes ?`
- `Mes ventes récentes ?`
- `Historique de mes ventes ?`
- `Combien j'ai vendu ?`
- `Total de mes ventes ?`
- `My sales?`
- `Recent sales?`
- `Sales history?`
- `How much did I sell?`

### Sales Details
- `Détails de mes ventes ?`
- `Mes factures de vente ?`
- `Mes remises accordées ?`
- `Quantité vendue ?`
- `Montant moyen par vente ?`
- `Sales details?`
- `Sales invoices?`
- `Discounts given?`
- `Average sale amount?`

### Sales Statistics
- `Statistiques de ventes ?`
- `Performance de ventes ?`
- `Évolution de mes ventes ?`
- `Mes meilleures ventes ?`
- `Tendances de ventes ?`
- `Sales statistics?`
- `Sales performance?`
- `Best selling items?`

---

## 🏭 **BUSINESS OPERATIONS QUESTIONS**

### Suppliers & Inventory
- `Mes fournisseurs ?`
- `Liste des fournisseurs ?`
- `Informations fournisseurs ?`
- `Mon stock ?`
- `Inventaire actuel ?`
- `My suppliers?`
- `Supplier information?`
- `Current inventory?`
- `Stock levels?`

### Products & Catalog
- `Mes produits ?`
- `Catalogue de produits ?`
- `Produits disponibles ?`
- `Prix des produits ?`
- `Produits en rupture ?`
- `My products?`
- `Product catalog?`
- `Available products?`
- `Out of stock items?`

### Business Analytics
- `Tableau de bord ?`
- `Statistiques business ?`
- `Performance globale ?`
- `Indicateurs clés ?`
- `Rapport d'activité ?`
- `Dashboard?`
- `Business statistics?`
- `Key performance indicators?`
- `Activity report?`

## 🔍 **COMPLEX QUESTIONS (Both Systems)**

### Summary Questions
- `Résumé de mon compte ?`
- `Toutes mes informations ?`
- `Mon profil complet ?`
- `Vue d'ensemble de mon compte ?`
- `Mes données personnelles ?`
- `Account summary?`
- `All my information?`
- `Complete profile?`

### Comparative Questions
- `Combien j'ai gagné vs dépensé ?`
- `Plus de crédits ou de débits ?`
- `Factures payées vs impayées ?`
- `Ventes vs achats ?`
- `Performance ce mois vs le mois dernier ?`
- `Earnings vs expenses?`
- `Sales vs purchases?`
- `This month vs last month?`

---

## 🎯 **TESTING TIPS**

### General Testing Guidelines
1. **Always choose option 1** when prompted for data source to get real database data
2. **Test with different usernames** (user1, user2, admin, testuser) to see user-specific data
3. **Try variations** of the same question to test AI understanding
4. **Mix French and English** questions to test language handling
5. **Test edge cases** like empty responses or error conditions
6. **Use different conversation IDs** to simulate different sessions

### Backend-Specific Testing
7. **Chat-MCP:** Focus on personal, medical, financial, and subscription data
8. **Win-MCP:** Focus on business, client, sales, and pharmacy operations
9. **Smart Routing:** Let the AI automatically choose the best backend
10. **Cross-Backend:** Ask questions that might require both systems

### User-Specific Data Testing
11. **user1:** Pharmacien Test with specific client and sales data
12. **user2:** Docteur Marie with different client and sales data
13. **admin:** Administrative user with elevated access
14. **testuser:** Basic test user for general functionality

---

## 📝 **Sample Test Sequences**

### Chat-MCP Test Sequence
```
1. "Bonjour" → Should get friendly response
2. "Quel est mon nom ?" → Should ask for source (1 or 2)
3. "1" → Should return user-specific name (e.g., "Test User1")
4. "Mon profil médical ?" → Should ask for source again
5. "1" → Should return medical profile data
6. "Mes factures ?" → Should ask for source
7. "1" → Should return real invoice data
```

### Win-MCP Test Sequence
```
1. "Hello" → Should get friendly response
2. "My client information?" → Should ask for source (1 or 2)
3. "1" → Should return client profile (e.g., "Pharmacien Test")
4. "My sales data?" → Should ask for source again
5. "1" → Should return sales statistics and history
6. "My suppliers?" → Should ask for source
7. "1" → Should return supplier information
```

### Cross-User Testing
```
# Test with user1
1. Username: "user1", Question: "My client info?" → "Pharmacien Test"
2. Username: "user1", Question: "My sales?" → User1's specific sales data

# Test with user2
3. Username: "user2", Question: "My client info?" → "Docteur Marie"
4. Username: "user2", Question: "My sales?" → User2's different sales data
```

### Smart Routing Test
```
1. "My medical profile?" → Should route to Chat-MCP automatically
2. "My sales data?" → Should route to Win-MCP automatically
3. "My name?" → Could route to either, user gets to choose
4. "Dashboard overview?" → Should route to Win-MCP for business data
```

---

## 🔧 **Available Test Users**

### Chat-MCP Users
- **user1:** Test User1 with complete medical and financial profile
- **user2:** Test User2 with different medical and financial data
- **admin:** Administrative user with full access
- **testuser:** Basic user for general testing

### Win-MCP Users
- **user1:** Pharmacien Test (client code: user1) with sales data
- **user2:** Docteur Marie (client code: user2) with different sales data
- **admin:** Administrateur Système with administrative access
- **testuser:** Basic business user

---

## 🌟 **Advanced Testing Scenarios**

### Multi-Turn Conversations
```
1. "Hello" → Friendly greeting
2. "What can you tell me about my account?" → Should ask for backend/source
3. "1" → Returns account summary
4. "Tell me more about my recent activity" → Should continue in same context
5. "What about my medical information?" → Should switch to Chat-MCP context
```

### Error Handling
```
1. "My xyz data?" → Should handle unknown data types gracefully
2. Empty username → Should handle missing user context
3. Invalid backend selection → Should provide helpful error message
4. Database connection issues → Should fallback appropriately
```

### Performance Testing
```
1. Multiple rapid questions → Should handle concurrent requests
2. Large data queries → Should handle pagination and large responses
3. Complex analytical questions → Should process and summarize effectively
```

---

**🎉 All questions will return real, user-specific data from the H2 databases when you choose option 1!**

**🔄 The system now supports complete user data isolation - different users get different data!**

**🤖 Smart AI routing automatically selects the best backend for your questions!**
