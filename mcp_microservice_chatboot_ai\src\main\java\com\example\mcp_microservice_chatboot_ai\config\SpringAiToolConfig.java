package com.example.mcp_microservice_chatboot_ai.config;

import com.example.mcp_microservice_chatboot_ai.service.WinMcpSpecificToolsFunctions;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 🎯 Spring AI Tool Configuration
 * 
 * This configuration sets up Spring AI ChatClient with automatic @Tool function calling.
 * The ChatClient will automatically detect and use @Tool annotated methods from 
 * WinMcpSpecificToolsFunctions for intelligent tool selection.
 * 
 * Benefits:
 * ✅ True AI-powered tool selection (no hardcoded logic)
 * ✅ Automatic function calling based on @Tool descriptions
 * ✅ Handles variations, typos, and synonyms naturally
 * ✅ Context-aware tool selection
 */
@Configuration
public class SpringAiToolConfig {

    @Autowired
    private OpenAiChatModel openAiChatModel;

    @Autowired
    private WinMcpSpecificToolsFunctions winMcpSpecificTools;

    /**
     * Creates a ChatClient configured with Win-MCP specific tools
     * The ChatClient will automatically use @Tool annotated methods for function calling
     */
    @Bean
    public ChatClient winMcpChatClient() {
        return ChatClient.builder(openAiChatModel)
                .defaultSystem("""
                    You are a helpful pharmacy system assistant for WinPlus pharmacy management system.
                    You have access to specialized tools to retrieve different types of data for users.
                    
                    When a user asks a question, analyze their request and automatically call the most 
                    appropriate tool to get the requested information. Always provide a helpful and 
                    detailed response based on the tool results.
                    
                    Available tools:
                    - Client data tool: For questions about client information, customer details, profiles
                    - Sales data tool: For questions about sales, revenue, transactions, invoices, billing
                    - Product data tool: For questions about products, inventory, stock, medications
                    - Purchase data tool: For questions about purchases, suppliers, procurement
                    
                    Respond in French when appropriate, and always be helpful and professional.
                    """)
                .defaultTools(winMcpSpecificTools) // Spring AI will automatically detect @Tool methods
                .build();
    }

    /**
     * Creates a general ChatClient for non-tool-based conversations
     */
    @Bean
    public ChatClient generalChatClient() {
        return ChatClient.builder(openAiChatModel)
                .defaultSystem("""
                    You are a helpful AI assistant. Provide accurate, helpful, and professional responses.
                    If you don't know something, say so honestly. Always be respectful and courteous.
                    """)
                .build();
    }
}
