#!/usr/bin/env python3
import requests
import json
import time

def test_question(question):
    url = "http://localhost:8081/api/chat"
    payload = {
        "conversationId": f"test-{int(time.time())}",
        "username": "user1", 
        "content": question,
        "backend": "CHAT_MCP"
    }

    print(f"\n📝 Testing: {question}")
    print("=" * 50)
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get('content', 'No content')
            print(f"✅ Answer: {answer}")
            
            # Check if it's a "not found" response
            if "pas trouvé" in answer.lower() or "not found" in answer.lower():
                print("⚠️  WARNING: 'Not found' response detected")
        else:
            print(f"❌ Failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    time.sleep(2)  # Wait between requests

# Test the problematic questions
questions = [
    "My name?",           # This works
    "mes factures",       # This fails
    "mes transactions",   # This fails  
    "mes commandes",      # This fails
    "my financial summary" # This fails
]

print("🎯 TESTING PROBLEMATIC QUESTIONS")
print("=" * 70)
print("Watch the mcp_microservice_chatboot_ai terminal for debug logs!")
print()

for question in questions:
    test_question(question)

print("\n🎉 TEST COMPLETED")
print("Check the mcp_microservice_chatboot_ai terminal for debug output!")
