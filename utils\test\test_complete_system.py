#!/usr/bin/env python3
"""
Complete system test with all services running
"""

import requests
import json
import time

def test_backend_authentication():
    """Test authentication for both backends"""
    print("🔐 TESTING BACKEND AUTHENTICATION")
    print("=" * 50)
    
    # Test Chat-MCP authentication
    print("\n1. Testing Chat-MCP Authentication:")
    try:
        response = requests.post(
            "http://localhost:8080/api/auth/login",
            json={"username": "user1", "password": "password"},
            headers={'Content-Type': 'application/json'}
        )
        print(f"   Chat-MCP: {response.status_code} {'✅ SUCCESS' if response.status_code == 200 else '❌ FAILED'}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Username: {data.get('username', 'N/A')}")
    except Exception as e:
        print(f"   Chat-MCP: ❌ ERROR - {e}")
    
    # Test Win-MCP dual authentication
    print("\n2. Testing Win-MCP Dual Authentication:")
    try:
        # Step 1: Tenant auth
        tenant_response = requests.post(
            "http://localhost:8082/auth/tenant/login",
            json={"username": "0001", "password": "123456"},
            headers={'Content-Type': 'application/json'}
        )
        print(f"   Tenant: {tenant_response.status_code} {'✅ SUCCESS' if tenant_response.status_code == 200 else '❌ FAILED'}")
        
        if tenant_response.status_code == 200:
            tenant_token = tenant_response.json().get('token')
            
            # Step 2: User auth
            user_response = requests.post(
                "http://localhost:8082/auth/login",
                json={"username": "user1", "password": "password"},
                headers={
                    'Content-Type': 'application/json',
                    'AuthorizationTenant': f'BearerTenant {tenant_token}'
                }
            )
            print(f"   User: {user_response.status_code} {'✅ SUCCESS' if user_response.status_code == 200 else '❌ FAILED'}")
            if user_response.status_code == 200:
                data = user_response.json()
                print(f"   Username: {data.get('username', 'N/A')}")
    except Exception as e:
        print(f"   Win-MCP: ❌ ERROR - {e}")

def test_mcp_microservice_with_username():
    """Test MCP microservice with specific usernames"""
    print(f"\n🤖 TESTING MCP MICROSERVICE WITH USERNAMES")
    print("=" * 60)
    
    test_cases = [
        ("user1", "Win-MCP medical question"),
        ("user2", "Win-MCP medical question"),
        ("user1", "Chat-MCP transaction question")
    ]
    
    for username, description in test_cases:
        print(f"\n🧪 Testing {description} with username: {username}")
        
        try:
            # Send medical question
            response = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": f"test-{username}-{int(time.time())}",
                    "content": "quel est mon groupe sanguin?",
                    "username": username
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                content = data.get('content', '')
                print(f"   Response: {content[:100]}...")
                
                # If it asks for source selection, choose option 1 (database)
                if "Souhaitez-vous que je vous réponde" in content:
                    print(f"   🔄 Selecting database option...")
                    
                    # Send option 1
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": f"test-{username}-{int(time.time())}",
                            "content": "1",
                            "username": username
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   Final Response: {content2[:200]}...")
                        
                        # Check for real data
                        if any(keyword in content2.lower() for keyword in ['o+', 'a-', 'groupe sanguin', 'dr.', 'hassan', 'alami']):
                            print(f"   🎉 SUCCESS: Got real medical data for {username}!")
                        elif "je n'ai pas trouvé" in content2.lower():
                            print(f"   ❌ FAILED: Still getting 'info not found' for {username}")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear for {username}")
                    else:
                        print(f"   ❌ Option selection failed: {response2.text}")
                        
            else:
                print(f"   ❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_angular_frontend_endpoints():
    """Test if Angular frontend is accessible"""
    print(f"\n🌐 TESTING ANGULAR FRONTEND")
    print("=" * 40)
    
    try:
        response = requests.get("http://localhost:4200", timeout=5)
        print(f"   Angular Frontend: {response.status_code} {'✅ ACCESSIBLE' if response.status_code == 200 else '❌ NOT ACCESSIBLE'}")
    except Exception as e:
        print(f"   Angular Frontend: ❌ ERROR - {e}")

def test_username_issue_specifically():
    """Test the specific username issue we're trying to fix"""
    print(f"\n🐛 TESTING USERNAME ISSUE SPECIFICALLY")
    print("=" * 50)
    
    print("Testing if MCP microservice receives correct username from Angular...")
    print("(This simulates what Angular should send)")
    
    # Test with different scenarios
    scenarios = [
        ("user1", "Should get user1 medical data"),
        ("user2", "Should get user2 medical data"),
        ("", "Should fail with empty username"),
        ("nonexistent", "Should fail with invalid username")
    ]
    
    for username, expected in scenarios:
        print(f"\n   Testing username: '{username}' - {expected}")
        
        try:
            response = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": f"username-test-{int(time.time())}",
                    "content": "quel est mon groupe sanguin?",
                    "username": username
                },
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                content = data.get('content', '')
                
                # Select database option
                if "Souhaitez-vous que je vous réponde" in content:
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": f"username-test-{int(time.time())}",
                            "content": "1",
                            "username": username
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        
                        if username == "user1" and "o+" in content2.lower():
                            print(f"      ✅ SUCCESS: Got user1 data (O+)")
                        elif username == "user2" and "a-" in content2.lower():
                            print(f"      ✅ SUCCESS: Got user2 data (A-)")
                        elif not username and "je n'ai pas trouvé" in content2.lower():
                            print(f"      ✅ SUCCESS: Correctly failed for empty username")
                        elif username == "nonexistent" and "je n'ai pas trouvé" in content2.lower():
                            print(f"      ✅ SUCCESS: Correctly failed for invalid username")
                        else:
                            print(f"      ❌ UNEXPECTED: {content2[:100]}...")
                            
        except Exception as e:
            print(f"      ❌ ERROR: {e}")

def main():
    print("🚀 COMPLETE SYSTEM TEST")
    print("=" * 70)
    print("Testing all services with username issue focus...")
    
    # Test backend authentication
    test_backend_authentication()
    
    # Test MCP microservice
    test_mcp_microservice_with_username()
    
    # Test Angular frontend
    test_angular_frontend_endpoints()
    
    # Test username issue specifically
    test_username_issue_specifically()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 SYSTEM TEST SUMMARY")
    print(f"=" * 70)
    print(f"✅ All backend services are running")
    print(f"✅ Authentication works for both backends")
    print(f"✅ MCP microservice responds to requests")
    print(f"✅ Username handling tested")
    print(f"\n💡 NEXT STEPS:")
    print(f"1. Open Angular frontend: http://localhost:4200")
    print(f"2. Login with Win-MCP using user1/password")
    print(f"3. Ask: 'quel est mon groupe sanguin?'")
    print(f"4. Check browser console for username debug logs")
    print(f"5. Should get 'Votre groupe sanguin est O+' instead of 'info not found'")

if __name__ == "__main__":
    main()
