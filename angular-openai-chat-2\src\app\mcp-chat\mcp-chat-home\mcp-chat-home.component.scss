// Professional MCP Chat Home Component Design (Identical to Direct OpenAI)

// Professional Design System Variables (Matching Widget)
$primary-color: #667eea;
$primary-light: #f7fafc;
$primary-dark: #5a67d8;
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$secondary-color: #f8fafc;
$accent-color: #4fd1c7;
$text-color: #2d3748;
$text-light: #718096;
$text-muted: #a0aec0;
$border-color: #e2e8f0;
$background-color: #ffffff;
$surface-color: #f7fafc;
$shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
$font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;

// Spacing System
$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 1rem;      // 16px
$spacing-lg: 1.5rem;    // 24px
$spacing-xl: 2rem;      // 32px

// Border Radius
$radius-sm: 0.375rem;   // 6px
$radius-md: 0.5rem;     // 8px
$radius-lg: 0.75rem;    // 12px
$radius-xl: 1rem;       // 16px

// Breakpoints
$mobile-breakpoint: 480px;
$tablet-breakpoint: 768px;

.home-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
  background: transparent;
  font-family: $font-family;
  padding: $spacing-xl $spacing-lg;
  gap: $spacing-xl;
  position: relative;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 2px;
  }
}

// Professional Hero Section for MCP
.hero-section {
  text-align: center;
  // padding: $spacing-xl 0;
  position: relative;
  z-index: 1;

  .welcome-content {
    margin-bottom: $spacing-xl;

    .welcome-title {
      font-size: 32px;
      font-weight: 700;
      color: $text-color;
      margin: 0 0 $spacing-md 0;
      letter-spacing: -0.025em;

      @media (max-width: $mobile-breakpoint) {
        font-size: 28px;
      }
    }

    .welcome-subtitle {
      font-size: 18px;
      color: $text-light;
      margin: 0 0 $spacing-md 0;
      font-weight: 500;
      line-height: 1.4;

      @media (max-width: $mobile-breakpoint) {
        font-size: 16px;
      }
    }

    .info-badge {
      font-size: 14px;
      color: $primary-color;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
      padding: $spacing-sm $spacing-md;
      border-radius: $radius-lg;
      display: inline-block;
      border: 1px solid rgba(102, 126, 234, 0.2);
      backdrop-filter: blur(10px);
      margin: 0;
      font-weight: 500;
    }
  }

  .user-info-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: $radius-lg;
    padding: $spacing-lg;
    margin-bottom: $spacing-xl;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: $shadow-sm;

    .user-details {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-md;
      margin-bottom: $spacing-md;

      .user-avatar {
        i {
          font-size: 32px;
          color: $primary-color;
        }
      }

      .user-text {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .username {
          font-weight: 700;
          color: $text-color;
          font-size: 18px;
          letter-spacing: -0.025em;
        }

        .backend-badge {
          background: $primary-gradient;
          color: white;
          padding: $spacing-xs $spacing-sm;
          border-radius: $radius-xl;
          font-size: 11px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.025em;
          box-shadow: $shadow-sm;
          margin-top: $spacing-xs;
        }
      }
    }

    .logout-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-sm;
      width: 100%;
      padding: $spacing-md;
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
      color: white;
      border: none;
      border-radius: $radius-lg;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: $shadow-sm;
      letter-spacing: 0.025em;

      &:hover {
        background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
        transform: translateY(-2px);
        box-shadow: $shadow-md;
      }

      &:active {
        transform: translateY(0);
      }

      i {
        font-size: 16px;
      }
    }
  }

  .start-chat-button {
    background: $primary-gradient;
    color: white;
    border: none;
    border-radius: $radius-xl;
    padding: $spacing-lg $spacing-xl;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: $shadow-md;
    min-width: 280px;

    .button-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: $spacing-md;

      i {
        font-size: 20px;
      }
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
      background: linear-gradient(135deg, $primary-dark 0%, #6b46c1 100%);
    }

    &:active {
      transform: translateY(0);
    }

    @media (max-width: $mobile-breakpoint) {
      min-width: 100%;
      padding: $spacing-md $spacing-lg;
    }
  }
}

// Professional Quick Actions Section for MCP
.quick-actions-section {
  .section-title {
    font-size: 20px;
    font-weight: 700;
    color: $text-color;
    margin: 0 0 $spacing-lg 0;
    letter-spacing: -0.025em;
  }

  .action-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-sm;
    width: 100%;
    max-width: 100%;

    @media (max-width: $mobile-breakpoint) {
      grid-template-columns: 1fr;
      gap: $spacing-md;
    }

    .action-card {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: $radius-lg;
      padding: $spacing-md;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: $shadow-sm;
      text-align: left;
      display: flex;
      align-items: flex-start;
      gap: $spacing-sm;
      min-width: 0; // Prevent flex items from overflowing
      width: 100%;
      box-sizing: border-box;

      &:hover {
        transform: translateY(-2px);
        box-shadow: $shadow-md;
        background: rgba(255, 255, 255, 0.95);
        border-color: rgba(102, 126, 234, 0.2);
      }

      .action-icon {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        border-radius: $radius-md;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 20px;
          color: $primary-color;
        }
      }

      .action-content {
        flex: 1;
        min-width: 0; // Prevent text overflow

        h3 {
          font-size: 14px;
          font-weight: 600;
          color: $text-color;
          margin: 0 0 $spacing-xs 0;
          letter-spacing: -0.025em;
          line-height: 1.2;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        p {
          font-size: 12px;
          color: $text-light;
          margin: 0;
          line-height: 1.3;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
}

// Professional Popular Topics Section for MCP
.popular-topics-section {
  .section-title {
    font-size: 20px;
    font-weight: 700;
    color: $text-color;
    margin: 0 0 $spacing-lg 0;
    letter-spacing: -0.025em;
  }

  .topics-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;

    .topic-item {
      background: rgba(255, 255, 255, 0.7);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: $radius-lg;
      padding: $spacing-md $spacing-lg;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: $shadow-sm;
      display: flex;
      justify-content: space-between;
      align-items: center;
      text-align: left;

      &:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-1px);
        box-shadow: $shadow-md;
        border-color: rgba(102, 126, 234, 0.2);

        .topic-arrow {
          color: $primary-color;
          transform: translateX(4px);
        }
      }

      .topic-text {
        font-size: 14px;
        color: $text-color;
        font-weight: 500;
        letter-spacing: -0.025em;
        flex: 1;
        line-height: 1.4;
      }

      .topic-arrow {
        color: $text-muted;
        font-size: 16px;
        transition: all 0.3s ease;
        margin-left: $spacing-md;
      }
    }
  }
}
