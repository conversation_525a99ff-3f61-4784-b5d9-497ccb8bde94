# 🎯 Knowledge Toggle Implementation Guide

## 📋 Overview

This document explains the implementation of the Knowledge Source Toggle functionality that replaces the old "ask for source selection" system with a frontend toggle.

## 🔄 What Changed

### **Before (Old System):**
1. User asks a question
2. AI responds: "Choose 1 for database or 2 for general knowledge"
3. User types "1" or "2"
4. AI processes the original question with selected source

### **After (New System):**
1. User sets their preferred mode using the toggle (Global/Interne)
2. User asks a question
3. AI immediately responds using the selected knowledge source
4. No interruption or additional prompts

## 🎨 Frontend Changes (Angular)

### 1. **Toggle Component** (`mcp-chat-widget.component.html`)
- Added toggle in header: "Global" ↔ "Interne"
- Only visible when authenticated and in chat/home tabs
- Visual feedback with sliding animation

### 2. **State Management** (`mcp-chat-widget.component.ts`)
- `knowledgeMode: 'general' | 'internal' = 'internal'` (defaults to internal)
- `toggleKnowledgeMode()` method to switch modes
- localStorage persistence: `mcp_knowledge_mode`
- Passes mode to service with every message

### 3. **Service Updates** (`mcp.service.ts`)
- Updated `McpChatRequest` interface with `knowledgeMode` field
- Modified `sendMessage()` to accept and send knowledge mode
- Enhanced logging for debugging

## ⚙️ Backend Changes (MCP Microservice)

### 1. **Request DTO** (`ChatRequest.java`)
- Added `knowledgeMode` field to accept mode from frontend

### 2. **Service Logic** (`AiChatService.java`)
- **Completely refactored** `processChat()` method
- **Removed** conversation state management system
- **Simplified** flow: greeting detection → mode-based routing
- **Enhanced** logging for debugging

### 3. **Controller Updates** (`ChatController.java`)
- Created `McpChatResponseDto` to match Angular interface
- Fixed response format compatibility issues
- Added comprehensive logging

## 🔧 How It Works

### **Request Flow:**
```
Angular Widget → MCP Service → MCP Microservice → Backend APIs
     ↓              ↓              ↓                ↓
Toggle State → knowledgeMode → Route Decision → Data Source
```

### **Mode Routing:**
- `"general"` → Uses OpenAI general knowledge only
- `"internal"` → Uses database/backend data (Chat-MCP or Win-MCP)
- Greeting detection works regardless of mode

## 🧪 Testing

### **1. Start All Services:**
```bash
# Terminal 1: Chat-MCP Backend
cd chat-mcp && mvn spring-boot:run

# Terminal 2: Win-MCP Backend  
cd win-mcp && mvn spring-boot:run

# Terminal 3: MCP Microservice
cd mcp_microservice_chatboot_ai && mvn spring-boot:run

# Terminal 4: Angular Frontend
cd angular-openai-chat-2 && npm start
```

### **2. Manual Testing:**
1. Open browser: `http://localhost:4200`
2. Login to MCP widget
3. Notice toggle in header: "Global" ↔ "Interne"
4. Test both modes with different questions

### **3. Automated Testing:**
```bash
python test_knowledge_toggle.py
```

## 🐛 Troubleshooting

### **Issue 1: "Info not found" for Internal Mode**
**Symptoms:** All database questions return "not found" responses
**Causes:**
- Backend services (chat-mcp, win-mcp) not running
- Authentication issues with backends
- Network connectivity problems

**Solutions:**
1. Verify all services are running: `python test_knowledge_toggle.py`
2. Check MCP microservice logs for detailed errors
3. Verify backend authentication credentials

### **Issue 2: General Mode Not Working**
**Symptoms:** General questions still try to use database
**Causes:**
- Frontend not sending correct `knowledgeMode`
- Backend not processing mode parameter correctly

**Solutions:**
1. Check browser console for Angular logs
2. Check MCP microservice logs for mode detection
3. Verify request payload includes `knowledgeMode`

### **Issue 3: Toggle Not Visible**
**Symptoms:** Toggle doesn't appear in widget header
**Causes:**
- User not authenticated
- Not in chat/home tab
- CSS styling issues

**Solutions:**
1. Ensure user is logged in
2. Navigate to chat or home tab
3. Check browser developer tools for CSS issues

## 📊 Debug Logging

### **Angular Console:**
```
🚀 Sending request to MCP server: http://localhost:8081/api/chat
🧠 Using knowledge mode: internal
📋 Request payload: {conversationId: "...", content: "...", knowledgeMode: "internal"}
```

### **MCP Microservice Logs:**
```
🔄 CHAT CONTROLLER: Received message: mes factures
🧠 CHAT CONTROLLER: Knowledge mode: internal
🔍 GENERATE DATABASE RESPONSE: Starting for backend: win-mcp
✅ Win-MCP data retrieved successfully. Length: 1234
```

## 🎯 Key Benefits

1. **Better UX:** No interruptions asking for source selection
2. **User Control:** Easy mode switching with visual feedback
3. **Persistent Preference:** Choice remembered across sessions
4. **Cleaner Code:** Removed complex conversation state management
5. **Immediate Response:** Questions answered immediately

## 📝 Files Modified

### **Frontend (Angular):**
- `mcp-chat-widget.component.html` - Added toggle UI
- `mcp-chat-widget.component.ts` - Added toggle logic
- `mcp-chat-widget.component.scss` - Added toggle styling
- `mcp.service.ts` - Updated request interface

### **Backend (Java):**
- `ChatRequest.java` - Added knowledgeMode field
- `AiChatService.java` - Refactored processing logic
- `ChatController.java` - Fixed response format
- `McpChatResponseDto.java` - New response DTO

### **Testing:**
- `test_knowledge_toggle.py` - Automated test script
- `KNOWLEDGE_TOGGLE_IMPLEMENTATION.md` - This documentation

## 🚀 Next Steps

1. **Test thoroughly** with both modes
2. **Monitor logs** for any issues
3. **Verify persistence** across browser sessions
4. **Test edge cases** (network failures, backend errors)
5. **User feedback** on UX improvements

The implementation is now complete and ready for production use! 🎉
