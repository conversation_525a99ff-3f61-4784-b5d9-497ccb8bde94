# 🎯 **COMPLETE GUIDE: Adding New Data Types to Win-MCP System**

## 📋 **OVERVIEW**
This guide shows you exactly how to add new data types to the Win-MCP system. We'll use **"Suppliers/Fournisseurs"** as an example.

---

## **🔧 STEP 1: Backend (Win-MCP) Changes**

### **A. Create New Entity**
```java
// win-mcp/src/main/java/com/chatbootmcp/chatmcp/entity/Fournisseur.java
package com.chatbootmcp.chatmcp.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "fournisseurs")
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Fournisseur {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "code_fournisseur", unique = true)
    private String code;
    
    @Column(name = "nom")
    private String nom;
    
    @Column(name = "email")
    private String email;
    
    @Column(name = "telephone")
    private String telephone;
    
    @Column(name = "adresse")
    private String adresse;
    
    @Column(name = "chiffre_affaires")
    private BigDecimal chiffreAffaires = BigDecimal.ZERO;
    
    @Column(name = "actif")
    private Boolean actif = true;
    
    @Column(name = "date_creation")
    private LocalDateTime dateCreation = LocalDateTime.now();
    
    // Add relationships if needed
    @OneToMany(mappedBy = "fournisseur", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Achat> achats;
}
```

### **B. Create Repository**
```java
// win-mcp/src/main/java/com/chatbootmcp/chatmcp/repository/FournisseurRepository.java
package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Fournisseur;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface FournisseurRepository extends JpaRepository<Fournisseur, Long> {
    Optional<Fournisseur> findByCode(String code);
    List<Fournisseur> findByActifTrue();
    List<Fournisseur> findByNomContainingIgnoreCase(String nom);
}
```

### **C. Create Service**
```java
// win-mcp/src/main/java/com/chatbootmcp/chatmcp/service/FournisseurService.java
package com.chatbootmcp.chatmcp.service;

import com.chatbootmcp.chatmcp.entity.Fournisseur;
import com.chatbootmcp.chatmcp.repository.FournisseurRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;

@Service
public class FournisseurService {
    
    @Autowired
    private FournisseurRepository fournisseurRepository;
    
    public Map<String, Object> getAllFournisseurs(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Fournisseur> fournisseurPage = fournisseurRepository.findAll(pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("fournisseurs", fournisseurPage.getContent());
        response.put("totalItems", fournisseurPage.getTotalElements());
        response.put("totalPages", fournisseurPage.getTotalPages());
        response.put("currentPage", page);
        
        return response;
    }
    
    public Map<String, Object> getActiveFournisseurs(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Fournisseur> fournisseurPage = fournisseurRepository.findByActifTrue(pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("fournisseurs", fournisseurPage.getContent());
        response.put("totalItems", fournisseurPage.getTotalElements());
        response.put("totalPages", fournisseurPage.getTotalPages());
        response.put("currentPage", page);
        
        return response;
    }
}
```

### **D. Add Controller Endpoint**
```java
// Add to win-mcp/src/main/java/com/chatbootmcp/chatmcp/controller/WinPlusController.java

@Autowired
private FournisseurService fournisseurService;

@GetMapping("/api/winplus/fournisseurs")
public ResponseEntity<Map<String, Object>> getFournisseurs(
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(defaultValue = "false") boolean activeOnly) {
    
    try {
        Map<String, Object> result;
        if (activeOnly) {
            result = fournisseurService.getActiveFournisseurs(page, size);
        } else {
            result = fournisseurService.getAllFournisseurs(page, size);
        }
        return ResponseEntity.ok(result);
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to fetch suppliers: " + e.getMessage()));
    }
}
```

### **E. Add Sample Data (Optional)**
```java
// Add to win-mcp/src/main/java/com/chatbootmcp/chatmcp/service/RealDataSimulationWinplus.java

private void createSampleFournisseurs() {
    if (fournisseurRepository.count() == 0) {
        System.out.println("Creating sample suppliers...");
        
        Fournisseur f1 = new Fournisseur();
        f1.setCode("FOUR001");
        f1.setNom("Laboratoires Pharma Plus");
        f1.setEmail("<EMAIL>");
        f1.setTelephone("0522334455");
        f1.setAdresse("Zone Industrielle, Casablanca");
        f1.setChiffreAffaires(new BigDecimal("125000.00"));
        f1.setActif(true);
        
        Fournisseur f2 = new Fournisseur();
        f2.setCode("FOUR002");
        f2.setNom("MediSupply Morocco");
        f2.setEmail("<EMAIL>");
        f2.setTelephone("0537445566");
        f2.setAdresse("Technopolis, Rabat");
        f2.setChiffreAffaires(new BigDecimal("89500.00"));
        f2.setActif(true);
        
        fournisseurRepository.saveAll(Arrays.asList(f1, f2));
        System.out.println("✅ Sample suppliers created successfully");
    }
}

// Call this method in createSampleData()
```

---

## **🔧 STEP 2: MCP Microservice Changes**

### **A. Add Endpoint Call in Smart Function**
The code has already been updated in `WinMcpToolsFunctions.java` to include suppliers data:

```java
// Try to get suppliers data (NEW DATA TYPE EXAMPLE)
Mono<Map> suppliersDataMono = webClient.get()
        .uri("/api/winplus/fournisseurs?size=10")
        .retrieve()
        .bodyToMono(Map.class)
        .doOnSuccess(data -> System.out.println("✅ Suppliers data fetched successfully"))
        .doOnError(error -> System.out.println("⚠️ Suppliers data failed: " + error.getMessage()))
        .onErrorReturn(new HashMap<>());
```

### **B. Update Mono.zip and Data Processing**
```java
// Combine all data including suppliers
return Mono.zip(userDataMono, clientDataMono, salesDataMono, productsDataMono, 
               dashboardDataMono, clientSalesDataMono, medicalProfileMono, suppliersDataMono)
        .map(tuple -> {
            // Extract all data including suppliers
            Map<String, Object> suppliersData = (Map<String, Object>) tuple.getT8();
            
            // Process and format data
            return formatSmartWinMcpData(userData, clientData, salesStats, productsData, 
                                       dashboardData, clientSalesData, medicalProfileData, suppliersData);
        });
```

### **C. Add Data Formatting Section**
The formatting section has been added to handle suppliers data:

```java
// Suppliers Database Section (NEW DATA TYPE EXAMPLE)
if (suppliersData != null && suppliersData.containsKey("fournisseurs")) {
    List<Map<String, Object>> fournisseurs = (List<Map<String, Object>>) suppliersData.get("fournisseurs");
    int totalItems = suppliersData.containsKey("totalItems") ? (Integer) suppliersData.get("totalItems") : fournisseurs.size();
    
    sb.append("🏭 BASE DE DONNÉES FOURNISSEURS (").append(totalItems).append(" fournisseurs):\n");
    
    // Process and display supplier data with analytics
    // ... (formatting logic)
}
```

---

## **🎯 STEP 3: Database Schema (Optional)**

### **Add Table to Database Schema**
```sql
-- Add to database_schema_winplus.sql
CREATE TABLE fournisseurs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    code_fournisseur VARCHAR(50) UNIQUE NOT NULL,
    nom VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    telephone VARCHAR(20),
    adresse TEXT,
    chiffre_affaires DECIMAL(15,2) DEFAULT 0.00,
    actif BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add sample data
INSERT INTO fournisseurs (code_fournisseur, nom, email, telephone, adresse, chiffre_affaires, actif) VALUES
('FOUR001', 'Laboratoires Pharma Plus', '<EMAIL>', '0522334455', 'Zone Industrielle, Casablanca', 125000.00, TRUE),
('FOUR002', 'MediSupply Morocco', '<EMAIL>', '0537445566', 'Technopolis, Rabat', 89500.00, TRUE);
```

---

## **🚀 STEP 4: Testing**

### **Test the New Endpoints**
```bash
# Test suppliers endpoint
curl -H "authorizationTenant: Bearer tenant_token" \
     -H "Authorization: Bearer user_token" \
     http://localhost:8082/api/winplus/fournisseurs

# Test with MCP microservice
curl -X POST http://localhost:8081/api/mcp/smart-winmcp \
     -H "Content-Type: application/json" \
     -d '{"username": "testuser", "question": "my suppliers?"}'
```

---

## **📝 SUMMARY: What You Need to Change**

### **For ANY New Data Type:**

1. **Backend (Win-MCP):**
   - ✅ Create Entity class
   - ✅ Create Repository interface  
   - ✅ Create Service class
   - ✅ Add Controller endpoint
   - ✅ Add sample data (optional)

2. **MCP Microservice:**
   - ✅ Add endpoint call in `getSmartWinMcpDataTool()`
   - ✅ Update `Mono.zip()` to include new data
   - ✅ Update `formatSmartWinMcpData()` method signature
   - ✅ Add formatting section for new data type

3. **Database (Optional):**
   - ✅ Add table schema
   - ✅ Add sample data

### **🎯 The Pattern is Always the Same:**
1. **Entity** → **Repository** → **Service** → **Controller** → **Endpoint**
2. **MCP Call** → **Data Processing** → **Formatting** → **AI Response**

This modular approach makes it easy to add any new data type to the Win-MCP system! 🚀
