<!-- Professional Clean Home Page Structure -->
<div class="home-container app-chat-home-root">
  <!-- Hero Section with Clean Welcome -->
  <section class="hero-section" aria-label="Section d'accueil">
    <!-- Welcome Message -->
    <div class="welcome-content">
      <h1 class="welcome-title">Bonjour 👋</h1>
      <p class="welcome-subtitle">Comment puis-je vous aider aujourd'hui ?</p>
    </div>

    <!-- Main Action Button -->
    <button class="start-chat-button"
            (click)="onAskQuestion()"
            aria-label="Commencer une nouvelle conversation">
      <div class="button-content">
        <i class="mdi mdi-message-text-outline" aria-hidden="true"></i>
        <span>Commencer une conversation</span>
      </div>
    </button>
  </section>

  <!-- Quick Actions Section -->
  <section class="quick-actions-section" aria-label="Actions rapides">
    <h2 class="section-title">Actions rapides</h2>

    <div class="action-grid">
      <button class="action-card"
              (click)="onSelectQuestion('Comment puis-je vous aider avec mes questions techniques ?')"
              aria-label="Poser une question technique">
        <div class="action-icon">
          <i class="mdi mdi-help-circle-outline" aria-hidden="true"></i>
        </div>
        <div class="action-content">
          <h3>Questions techniques</h3>
          <p>Obtenez de l'aide sur vos problèmes techniques</p>
        </div>
      </button>

      <button class="action-card"
              (click)="onSelectQuestion('Pouvez-vous m\'expliquer comment utiliser cette fonctionnalité ?')"
              aria-label="Demander des explications">
        <div class="action-icon">
          <i class="mdi mdi-lightbulb-outline" aria-hidden="true"></i>
        </div>
        <div class="action-content">
          <h3>Explications</h3>
          <p>Comprenez mieux les fonctionnalités</p>
        </div>
      </button>

      <button class="action-card"
              (click)="onSelectQuestion('Avez-vous des conseils ou des bonnes pratiques à partager ?')"
              aria-label="Obtenir des conseils">
        <div class="action-icon">
          <i class="mdi mdi-star-outline" aria-hidden="true"></i>
        </div>
        <div class="action-content">
          <h3>Conseils</h3>
          <p>Découvrez les meilleures pratiques</p>
        </div>
      </button>

      <button class="action-card"
              (click)="onSelectQuestion('Pouvez-vous m\'aider à résoudre un problème spécifique ?')"
              aria-label="Résoudre un problème">
        <div class="action-icon">
          <i class="mdi mdi-tools" aria-hidden="true"></i>
        </div>
        <div class="action-content">
          <h3>Dépannage</h3>
          <p>Résolvez vos problèmes rapidement</p>
        </div>
      </button>
    </div>
  </section>

  <!-- Popular Topics Section -->
  <section class="popular-topics-section" aria-label="Sujets populaires">
    <h2 class="section-title">Sujets populaires</h2>

    <div class="topics-list">
      <button *ngFor="let question of suggestedQuestions"
              class="topic-item"
              (click)="onSelectQuestion(question.text)"
              [attr.aria-label]="'Poser la question: ' + question.text">
        <span class="topic-text">{{ question.text }}</span>
        <i class="mdi mdi-chevron-right topic-arrow" aria-hidden="true"></i>
      </button>
    </div>
  </section>
</div>
  