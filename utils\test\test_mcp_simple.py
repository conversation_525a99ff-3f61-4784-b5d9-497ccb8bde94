#!/usr/bin/env python3
"""
Simple test for MCP microservice to debug the 404 issue
"""

import requests
import json

def test_mcp_simple():
    """Test a simple call to MCP microservice"""
    
    url = "http://localhost:8081/api/mcp/smart-winmcp"
    payload = {
        "username": "testuser",
        "question": "hello"
    }
    
    print("🔍 Testing MCP microservice...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ MCP microservice is working!")
        else:
            print("❌ MCP microservice returned an error")
            
    except Exception as e:
        print(f"❌ Error calling MCP microservice: {str(e)}")

def test_mcp_endpoints():
    """Test different MCP endpoints to see which ones exist"""
    
    base_url = "http://localhost:8081"
    endpoints = [
        "/api/mcp/smart-winmcp",
        "/api/mcp/smart-chatmcp", 
        "/api/mcp/chat",
        "/actuator/health"
    ]
    
    print("\n🔍 Testing MCP endpoints...")
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        try:
            if endpoint == "/actuator/health":
                response = requests.get(url, timeout=10)
            else:
                payload = {"username": "testuser", "question": "test"}
                response = requests.post(url, json=payload, timeout=10)
            
            print(f"✅ {endpoint}: {response.status_code}")
            
        except Exception as e:
            print(f"❌ {endpoint}: {str(e)}")

if __name__ == "__main__":
    test_mcp_simple()
    test_mcp_endpoints()
