package com.example.mcp_microservice_chatboot_ai.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Configuration class for the MCP server.
 * This class sets up the necessary beans for the MCP server to function.
 */

/**
 * Role: Configuration class for the MCP server
    Purpose:
    Sets up CORS configuration to allow cross-origin requests from the Angular client
    Configures WebFlux transport for the MCP server
    Enables secure communication between the client and server
 */
@Configuration
public class McpServerConfig {

    /**
     * Configures CORS for the MCP server to allow cross-origin requests from the Angular client.
     * 
     * @return A CorsWebFilter bean that handles CORS requests
     */
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.setAllowCredentials(true);
        corsConfig.setAllowedOrigins(Arrays.asList("http://192.168.101.176:4200", "https://winpharmplus.ma")); // */*
        corsConfig.setAllowedHeaders(Arrays.asList("Origin", "Access-Control-Allow-Origin", "Content-Type",
                "Accept", "Authorization", "Origin, Accept", "X-Requested-With",
                "Access-Control-Request-Method", "Access-Control-Request-Headers"));
        corsConfig.setExposedHeaders(Arrays.asList("Origin", "Content-Type", "Accept", "Authorization",
                "Access-Control-Allow-Origin", "Access-Control-Allow-Credentials"));
        corsConfig.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);
        
        return new CorsWebFilter(source);
    }
}
