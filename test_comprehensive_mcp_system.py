#!/usr/bin/env python3
"""
Comprehensive test for the entire MCP system
Tests both Chat-MCP and Win-MCP backends with all possible questions
"""

import requests
import json
import time

def test_chat_mcp_authentication():
    """Test Chat-MCP authentication"""
    print("🔐 TESTING CHAT-MCP AUTHENTICATION")
    print("=" * 50)
    
    auth_url = "http://localhost:8080/api/auth/login"
    auth_data = {
        "username": "user1",
        "password": "password123"
    }
    
    try:
        response = requests.post(auth_url, json=auth_data)
        print(f"Auth Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            print(f"✅ Authentication successful")
            print(f"Token: {token[:50]}..." if token else "No token")
            return token
        else:
            print(f"❌ Authentication failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def test_chat_mcp_direct_endpoints(token):
    """Test Chat-MCP endpoints directly"""
    print("\n📊 TESTING CHAT-MCP DIRECT ENDPOINTS")
    print("=" * 50)
    
    if not token:
        print("❌ No token available")
        return
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    endpoints = [
        ("/api/users/data", "User Data"),
        ("/api/real-data/profile", "User Profile"),
        ("/api/real-data/invoices?limit=5", "Invoices"),
        ("/api/real-data/transactions?limit=5", "Transactions"),
        ("/api/real-data/orders?limit=5", "Orders"),
        ("/api/real-data/financial-summary", "Financial Summary")
    ]
    
    for endpoint, name in endpoints:
        try:
            url = f"http://localhost:8080{endpoint}"
            response = requests.get(url, headers=headers)
            print(f"\n{name}:")
            print(f"  URL: {url}")
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    print(f"  ✅ Success: {len(data)} items")
                    if data:
                        print(f"  Sample: {str(data[0])[:100]}...")
                elif isinstance(data, dict):
                    print(f"  ✅ Success: {len(data)} fields")
                    print(f"  Sample: {str(data)[:100]}...")
                else:
                    print(f"  ✅ Success: {data}")
            else:
                print(f"  ❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")

def test_mcp_microservice_chat_questions():
    """Test MCP microservice with Chat-MCP questions"""
    print("\n🤖 TESTING MCP MICROSERVICE - CHAT-MCP QUESTIONS")
    print("=" * 60)
    
    base_url = "http://localhost:8081/api/chat"
    conversation_id = f"test-chat-{int(time.time())}"
    
    questions = [
        "My name?",
        "What is my email?", 
        "Show me my invoices",
        "mes factures",
        "Show me my transactions",
        "mes transactions",
        "Show me my orders",
        "mes commandes",
        "What is my financial summary?",
        "mon résumé financier",
        "What is my current balance?",
        "Quel est mon solde actuel?"
    ]
    
    for question in questions:
        print(f"\n📝 Question: {question}")
        
        payload = {
            "conversationId": conversation_id,
            "username": "user1",
            "content": question,
            "backend": "CHAT_MCP"
        }
        
        try:
            response = requests.post(base_url, json=payload)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get('response', 'No response')
                print(f"✅ Answer: {answer[:200]}...")
                
                # Check if it's a "not found" response
                if "pas trouvé" in answer.lower() or "not found" in answer.lower():
                    print("⚠️  WARNING: 'Not found' response detected")
            else:
                print(f"❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        time.sleep(1)  # Small delay between requests

def test_win_mcp_authentication():
    """Test Win-MCP dual authentication"""
    print("\n🔐 TESTING WIN-MCP DUAL AUTHENTICATION")
    print("=" * 50)
    
    # Step 1: Tenant authentication
    tenant_auth_url = "http://localhost:8082/api/auth/tenant"
    tenant_data = {
        "tenantCode": "0001",
        "tenantPassword": "123456"
    }
    
    try:
        response = requests.post(tenant_auth_url, json=tenant_data)
        print(f"Tenant Auth Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            tenant_token = data.get('token')
            print(f"✅ Tenant authentication successful")
            
            # Step 2: User authentication
            user_auth_url = "http://localhost:8082/api/auth/login"
            user_data = {
                "username": "user2",
                "password": "password123"
            }
            
            headers = {'authorizationTenant': f'BearerTenant {tenant_token}'}
            response = requests.post(user_auth_url, json=user_data, headers=headers)
            print(f"User Auth Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                user_token = data.get('token')
                print(f"✅ User authentication successful")
                return tenant_token, user_token
            else:
                print(f"❌ User authentication failed: {response.text}")
                return tenant_token, None
        else:
            print(f"❌ Tenant authentication failed: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None, None

def test_mcp_microservice_win_questions():
    """Test MCP microservice with Win-MCP questions"""
    print("\n🤖 TESTING MCP MICROSERVICE - WIN-MCP QUESTIONS")
    print("=" * 60)
    
    base_url = "http://localhost:8081/api/chat"
    conversation_id = f"test-win-{int(time.time())}"
    
    questions = [
        "Show me client information",
        "mes informations client",
        "Show me sales data",
        "mes ventes",
        "Show me products",
        "les produits",
        "Show me purchases",
        "mes achats",
        "Dashboard summary",
        "résumé tableau de bord",
        "Client statistics",
        "statistiques client"
    ]
    
    for question in questions:
        print(f"\n📝 Question: {question}")
        
        payload = {
            "conversationId": conversation_id,
            "username": "user2",
            "content": question,
            "backend": "WIN_MCP"
        }
        
        try:
            response = requests.post(base_url, json=payload)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get('response', 'No response')
                print(f"✅ Answer: {answer[:200]}...")
                
                # Check if it's a "not found" response
                if "pas trouvé" in answer.lower() or "not found" in answer.lower():
                    print("⚠️  WARNING: 'Not found' response detected")
            else:
                print(f"❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        time.sleep(1)  # Small delay between requests

def main():
    print("🎯 COMPREHENSIVE MCP SYSTEM TEST")
    print("=" * 70)
    print("Testing complete MCP system: Chat-MCP + Win-MCP + MCP Microservice")
    print()
    
    # Test Chat-MCP
    chat_token = test_chat_mcp_authentication()
    if chat_token:
        test_chat_mcp_direct_endpoints(chat_token)
    
    # Test Win-MCP
    tenant_token, user_token = test_win_mcp_authentication()
    
    # Test MCP Microservice with both backends
    test_mcp_microservice_chat_questions()
    test_mcp_microservice_win_questions()
    
    print("\n🎉 COMPREHENSIVE TEST COMPLETED")
    print("=" * 70)
    print("Check the results above to identify any issues.")

if __name__ == "__main__":
    main()
