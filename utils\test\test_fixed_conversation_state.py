#!/usr/bin/env python3
"""
Test with proper conversation state management
"""

import requests
import json
import time

def test_proper_conversation_flow():
    """Test with proper conversation state management"""
    print("🔧 TESTING PROPER CONVERSATION FLOW")
    print("=" * 50)
    
    # Test user1 medical question
    conversation_id = f"test-user1-{int(time.time())}"
    username = "user1"
    
    print(f"\n🧪 Testing medical question for {username}")
    print(f"   Conversation ID: {conversation_id}")
    
    try:
        # Step 1: Send medical question
        response1 = requests.post(
            "http://localhost:8081/api/chat",
            json={
                "conversationId": conversation_id,  # Same ID
                "content": "quel est mon groupe sanguin?",
                "username": username
            },
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Step 1 Status: {response1.status_code}")
        if response1.status_code == 200:
            data1 = response1.json()
            content1 = data1.get('content', '')
            print(f"   Step 1 Response: {content1[:100]}...")
            
            # Step 2: Send option selection with SAME conversation ID
            if "Souhaitez-vous que je vous réponde" in content1:
                print(f"   🔄 Step 2: Selecting database option with SAME conversation ID...")
                
                response2 = requests.post(
                    "http://localhost:8081/api/chat",
                    json={
                        "conversationId": conversation_id,  # SAME ID!
                        "content": "1",
                        "username": username
                    },
                    headers={'Content-Type': 'application/json'}
                )
                
                print(f"   Step 2 Status: {response2.status_code}")
                if response2.status_code == 200:
                    data2 = response2.json()
                    content2 = data2.get('content', '')
                    print(f"   Step 2 Response: {content2[:200]}...")
                    
                    # Check for real data
                    if "o+" in content2.lower() and "groupe sanguin" in content2.lower():
                        print(f"   🎉 SUCCESS: Got real medical data for {username}!")
                        return True
                    elif "je n'ai pas trouvé" in content2.lower():
                        print(f"   ❌ FAILED: Still getting 'info not found' for {username}")
                        return False
                    else:
                        print(f"   ⚠️  UNCLEAR: Response unclear for {username}")
                        return False
                else:
                    print(f"   ❌ Step 2 failed: {response2.text}")
                    return False
        else:
            print(f"   ❌ Step 1 failed: {response1.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_multiple_users():
    """Test multiple users with proper conversation management"""
    print(f"\n👥 TESTING MULTIPLE USERS")
    print("=" * 40)
    
    users = ["user1", "user2"]
    results = {}
    
    for username in users:
        conversation_id = f"test-{username}-{int(time.time())}"
        print(f"\n🧪 Testing {username} with conversation ID: {conversation_id}")
        
        try:
            # Step 1: Medical question
            response1 = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": conversation_id,
                    "content": "quel est mon groupe sanguin?",
                    "username": username
                },
                headers={'Content-Type': 'application/json'}
            )
            
            if response1.status_code == 200:
                data1 = response1.json()
                content1 = data1.get('content', '')
                
                # Step 2: Select database option
                if "Souhaitez-vous que je vous réponde" in content1:
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": conversation_id,  # SAME ID
                            "content": "1",
                            "username": username
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        
                        if username == "user1" and "o+" in content2.lower():
                            print(f"   ✅ SUCCESS: {username} got O+ blood type")
                            results[username] = "SUCCESS"
                        elif username == "user2" and "a-" in content2.lower():
                            print(f"   ✅ SUCCESS: {username} got A- blood type")
                            results[username] = "SUCCESS"
                        else:
                            print(f"   ❌ FAILED: {username} got unexpected response")
                            results[username] = "FAILED"
                    else:
                        print(f"   ❌ FAILED: {username} step 2 failed")
                        results[username] = "FAILED"
                else:
                    print(f"   ❌ FAILED: {username} didn't get source selection")
                    results[username] = "FAILED"
            else:
                print(f"   ❌ FAILED: {username} step 1 failed")
                results[username] = "FAILED"
                
        except Exception as e:
            print(f"   ❌ ERROR: {username} - {e}")
            results[username] = "ERROR"
    
    return results

def main():
    print("🔧 TESTING CONVERSATION STATE FIX")
    print("=" * 70)
    
    # Test proper conversation flow
    success = test_proper_conversation_flow()
    
    # Test multiple users
    results = test_multiple_users()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 CONVERSATION STATE TEST RESULTS")
    print(f"=" * 70)
    
    if success:
        print(f"✅ Conversation state management: WORKING")
    else:
        print(f"❌ Conversation state management: FAILED")
    
    print(f"\n👥 User test results:")
    for user, result in results.items():
        status = "✅" if result == "SUCCESS" else "❌"
        print(f"   {status} {user}: {result}")
    
    all_success = success and all(r == "SUCCESS" for r in results.values())
    
    if all_success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The MCP microservice is working correctly.")
        print(f"The issue is likely in the Angular frontend conversation management.")
    else:
        print(f"\n⚠️  SOME TESTS FAILED")
        print(f"Need to investigate further.")
    
    print(f"\n💡 NEXT STEPS:")
    print(f"1. Check Angular frontend conversation ID management")
    print(f"2. Ensure same conversation ID is used for question and response")
    print(f"3. Check browser console for conversation ID logs")

if __name__ == "__main__":
    main()
