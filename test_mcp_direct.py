#!/usr/bin/env python3
import requests
import json

# Test MCP microservice directly
url = "http://localhost:8081/api/chat"
payload = {
    "conversationId": "test-123",
    "username": "user1", 
    "content": "My name?",
    "backend": "CHAT_MCP"
}

print("Testing MCP microservice...")
print(f"URL: {url}")
print(f"Payload: {json.dumps(payload, indent=2)}")

try:
    response = requests.post(url, json=payload, timeout=30)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"Error: {e}")
