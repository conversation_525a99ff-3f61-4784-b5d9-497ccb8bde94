package com.example.mcp_microservice_chatboot_ai.service;

import com.example.mcp_microservice_chatboot_ai.config.ApiUrlsConfig;
import com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.ai.openai.api.tool.OpenAiApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 🎯 SPECIFIC TOOLS APPROACH for Win-MCP Integration
 * 
 * This service provides SPECIFIC @Tool functions for efficient endpoint selection.
 * Instead of calling ALL endpoints, AI intelligently chooses the right tool based on user question.
 * 
 * Performance Benefits:
 * ✅ Only calls relevant endpoints
 * ✅ Reduced network overhead  
 * ✅ Faster response times
 * ✅ Better resource utilization
 */
@Service
public class WinMcpSpecificToolsFunctions {

    @Autowired
    private WinMcpAuthService winMcpAuthService;

    @Autowired
    private ApiUrlsConfig apiUrlsConfig;

    @Autowired
    private WebClient.Builder webClientBuilder;

    // ==================== HELPER METHODS ====================

    /**
     * Authenticate user with Win-MCP backend
     */
    private WinMcpAuthService.WinMcpAuthResult authenticateUser(String username) {
        try {
            WinMcpAuthService.WinMcpAuthResult authResult = winMcpAuthService.authenticateUser(username)
                    .subscribeOn(Schedulers.boundedElastic())
                    .timeout(Duration.ofSeconds(10))
                    .block();

            if (authResult == null || authResult.getUserToken() == null) {
                System.out.println("❌ Authentication failed for user: " + username);
                return null;
            }

            System.out.println("✅ Authentication successful for user: " + username);
            return authResult;
        } catch (Exception e) {
            System.out.println("❌ Authentication error: " + e.getMessage());
            return null;
        }
    }

    /**
     * Create authenticated WebClient with proper headers
     */
    private WebClient createAuthenticatedWebClient(WinMcpAuthService.WinMcpAuthResult authResult) {
        WebClient.Builder builder = WebClient.builder()
                .baseUrl(apiUrlsConfig.getWinMcpBaseUrl())
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        // Add authentication headers based on auth type
        Map<String, String> authHeaders = authResult.getHeaders();
        for (Map.Entry<String, String> header : authHeaders.entrySet()) {
            builder.defaultHeader(header.getKey(), header.getValue());
        }

        return builder.build();
    }

    // ==================== SPECIFIC @TOOL FUNCTIONS ====================

    /**
     * 👥 CLIENT DATA TOOL - For questions about clients, customers, customer information
     * Use this tool when user asks about: clients, customers, customer details, client profiles, customer management
     */
    // Note: @Tool annotation removed - using manual tool selection based on keywords
    public Function<UserProfileRequest, String> getWinMcpClientDataTool() {
        return request -> {
            try {
                System.out.println("👥 WIN-MCP CLIENT TOOL: Processing client question for user: " + request.username());

                // Authenticate with Win-MCP
                WinMcpAuthService.WinMcpAuthResult authResult = authenticateUser(request.username());
                if (authResult == null) {
                    return "Erreur d'authentification pour l'utilisateur: " + request.username();
                }

                // Create WebClient with authentication
                WebClient webClient = createAuthenticatedWebClient(authResult);

                // Fetch user-specific client data
                Map<String, Object> clientData = webClient.get()
                        .uri("/api/winplus/clients/code/" + request.username())
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ Client data fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ Client data failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>())
                        .block();

                return formatClientData(clientData, request.username());

            } catch (Exception e) {
                System.out.println("❌ WIN-MCP CLIENT TOOL Error: " + e.getMessage());
                return "Erreur lors de la récupération des données client: " + e.getMessage();
            }
        };
    }

    /**
     * 💰 SALES DATA TOOL - For questions about sales, revenue, transactions, invoices
     * Use this tool when user asks about: sales, revenue, transactions, invoices, ventes, chiffre d'affaires
     */
    // Note: @Tool annotation removed - using manual tool selection based on keywords
    public Function<UserProfileRequest, String> getWinMcpSalesDataTool() {
        return request -> {
            try {
                System.out.println("💰 WIN-MCP SALES TOOL: Processing sales question for user: " + request.username());

                // Authenticate with Win-MCP
                WinMcpAuthService.WinMcpAuthResult authResult = authenticateUser(request.username());
                if (authResult == null) {
                    return "Erreur d'authentification pour l'utilisateur: " + request.username();
                }

                // Create WebClient with authentication
                WebClient webClient = createAuthenticatedWebClient(authResult);

                // First get client data to get client ID
                Map<String, Object> clientData = webClient.get()
                        .uri("/api/winplus/clients/code/" + request.username())
                        .retrieve()
                        .bodyToMono(Map.class)
                        .onErrorReturn(new HashMap<>())
                        .block();

                // Get sales statistics
                Map<String, Object> salesStats = webClient.get()
                        .uri("/api/winplus/ventes/statistics")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ Sales statistics fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ Sales statistics failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>())
                        .block();

                // Get user-specific sales if client ID available
                Map<String, Object> clientSalesData = new HashMap<>();
                if (clientData != null && clientData.containsKey("id")) {
                    Long clientId = Long.valueOf(clientData.get("id").toString());
                    clientSalesData = webClient.get()
                            .uri("/api/winplus/ventes/client/" + clientId + "?size=10")
                            .retrieve()
                            .bodyToMono(Map.class)
                            .doOnSuccess(data -> System.out.println("✅ User-specific sales data fetched successfully"))
                            .doOnError(error -> System.out.println("⚠️ User-specific sales data failed: " + error.getMessage()))
                            .onErrorReturn(new HashMap<>())
                            .block();
                }

                return formatSalesData(salesStats, clientSalesData, request.username());

            } catch (Exception e) {
                System.out.println("❌ WIN-MCP SALES TOOL Error: " + e.getMessage());
                return "Erreur lors de la récupération des données de vente: " + e.getMessage();
            }
        };
    }

    /**
     * 📦 PRODUCT DATA TOOL - For questions about products, inventory, stock, medications
     * Use this tool when user asks about: products, inventory, stock, medications, produits, médicaments, stock
     */
    // Note: @Tool annotation removed - using manual tool selection based on keywords
    public Function<UserProfileRequest, String> getWinMcpProductDataTool() {
        return request -> {
            try {
                System.out.println("📦 WIN-MCP PRODUCT TOOL: Processing product question for user: " + request.username());

                // Authenticate with Win-MCP
                WinMcpAuthService.WinMcpAuthResult authResult = authenticateUser(request.username());
                if (authResult == null) {
                    return "Erreur d'authentification pour l'utilisateur: " + request.username();
                }

                // Create WebClient with authentication
                WebClient webClient = createAuthenticatedWebClient(authResult);

                // Fetch products data
                Map<String, Object> productsData = webClient.get()
                        .uri("/api/winplus/produits")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ Products data fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ Products data failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>())
                        .block();

                return formatProductData(productsData, request.username());

            } catch (Exception e) {
                System.out.println("❌ WIN-MCP PRODUCT TOOL Error: " + e.getMessage());
                return "Erreur lors de la récupération des données produits: " + e.getMessage();
            }
        };
    }

    /**
     * 🛒 PURCHASE DATA TOOL - For questions about purchases, suppliers, procurement
     * Use this tool when user asks about: purchases, suppliers, procurement, achats, fournisseurs
     */
    // Note: @Tool annotation removed - using manual tool selection based on keywords
    public Function<UserProfileRequest, String> getWinMcpPurchaseDataTool() {
        return request -> {
            try {
                System.out.println("🛒 WIN-MCP PURCHASE TOOL: Processing purchase question for user: " + request.username());

                // Authenticate with Win-MCP
                WinMcpAuthService.WinMcpAuthResult authResult = authenticateUser(request.username());
                if (authResult == null) {
                    return "Erreur d'authentification pour l'utilisateur: " + request.username();
                }

                // Create WebClient with authentication
                WebClient webClient = createAuthenticatedWebClient(authResult);

                // Fetch suppliers data
                Map<String, Object> suppliersData = webClient.get()
                        .uri("/api/winplus/fournisseurs")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ Suppliers data fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ Suppliers data failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>())
                        .block();

                // Fetch purchase statistics
                Map<String, Object> purchaseStats = webClient.get()
                        .uri("/api/winplus/achats")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ Purchase data fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ Purchase data failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>())
                        .block();

                return formatPurchaseData(suppliersData, purchaseStats, request.username());

            } catch (Exception e) {
                System.out.println("❌ WIN-MCP PURCHASE TOOL Error: " + e.getMessage());
                return "Erreur lors de la récupération des données d'achat: " + e.getMessage();
            }
        };
    }

    // ==================== FORMATTING METHODS ====================

    private String formatClientData(Map<String, Object> clientData, String username) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 👥 DONNÉES CLIENT WIN-MCP ===\n\n");
        
        if (clientData != null && !clientData.isEmpty() && clientData.containsKey("nom")) {
            sb.append("🏢 INFORMATIONS CLIENT:\n");
            sb.append("  • Nom: ").append(clientData.get("nom")).append(" ").append(clientData.get("prenom"));
            sb.append(" (").append(clientData.get("codeClient")).append(")\n");
            sb.append("  • Email: ").append(clientData.get("email")).append("\n");
            sb.append("  • Téléphone: ").append(clientData.get("gsm")).append("\n");
            sb.append("  • Adresse: ").append(clientData.get("adr1"));
            if (clientData.get("adr2") != null) {
                sb.append(", ").append(clientData.get("adr2"));
            }
            sb.append("\n");
            sb.append("  • Solde actuel: ").append(clientData.get("soldeClient")).append(" DH\n");
            sb.append("  • Plafond crédit: ").append(clientData.get("plafondCredit")).append(" DH\n");
            sb.append("  • Chiffre d'affaires: ").append(clientData.get("caClient")).append(" DH\n");
            sb.append("  • Taux remise: ").append(clientData.get("tauxRemise")).append("%\n");
            sb.append("  • Statut: ").append(clientData.get("estActif").equals(true) ? "Actif" : "Inactif").append("\n");
        } else {
            sb.append("❌ Aucune donnée client trouvée pour l'utilisateur: ").append(username).append("\n");
        }
        
        return sb.toString();
    }

    private String formatSalesData(Map<String, Object> salesStats, Map<String, Object> clientSalesData, String username) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 💰 DONNÉES VENTES WIN-MCP ===\n\n");

        // Global Sales Statistics
        if (salesStats != null && !salesStats.isEmpty()) {
            sb.append("📈 STATISTIQUES GLOBALES DES VENTES:\n");
            if (salesStats.containsKey("totalSales")) {
                sb.append("  • Total des ventes: ").append(salesStats.get("totalSales")).append(" DH\n");
            }
            if (salesStats.containsKey("numberOfSales")) {
                sb.append("  • Nombre de ventes: ").append(salesStats.get("numberOfSales")).append("\n");
            }
            if (salesStats.containsKey("averageSale")) {
                sb.append("  • Vente moyenne: ").append(salesStats.get("averageSale")).append(" DH\n");
            }
            sb.append("\n");
        }

        // User-specific sales data
        if (clientSalesData != null && clientSalesData.containsKey("ventes")) {
            List<Map<String, Object>> ventes = (List<Map<String, Object>>) clientSalesData.get("ventes");
            sb.append("💰 VENTES SPÉCIFIQUES DU CLIENT (").append(ventes.size()).append(" ventes):\n");

            double totalSales = 0.0;
            double totalRemise = 0.0;
            int totalQuantity = 0;

            for (Map<String, Object> vente : ventes) {
                sb.append("  • Vente #").append(vente.get("numVente"));
                sb.append(" - Date: ").append(vente.get("dateVente"));
                sb.append(" - Montant: ").append(vente.get("mntNetTtc")).append(" DH");
                sb.append(" - Qté: ").append(vente.get("totalQte"));
                sb.append(" - Remise: ").append(vente.get("mntRemiseTtc")).append(" DH\n");

                // Calculate totals
                if (vente.get("mntNetTtc") != null) {
                    totalSales += Double.parseDouble(vente.get("mntNetTtc").toString());
                }
                if (vente.get("mntRemiseTtc") != null) {
                    totalRemise += Double.parseDouble(vente.get("mntRemiseTtc").toString());
                }
                if (vente.get("totalQte") != null) {
                    String qteStr = vente.get("totalQte").toString();
                    totalQuantity += (int) Double.parseDouble(qteStr);
                }
            }

            sb.append("  📊 TOTAUX CALCULÉS:\n");
            sb.append("    - Total des ventes: ").append(String.format("%.2f", totalSales)).append(" DH\n");
            sb.append("    - Total des remises: ").append(String.format("%.2f", totalRemise)).append(" DH\n");
            sb.append("    - Quantité totale: ").append(totalQuantity).append("\n");
            sb.append("    - Vente moyenne: ").append(String.format("%.2f", totalSales / ventes.size())).append(" DH\n");
        } else {
            sb.append("❌ Aucune donnée de vente trouvée pour l'utilisateur: ").append(username).append("\n");
        }

        return sb.toString();
    }

    private String formatProductData(Map<String, Object> productsData, String username) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 📦 DONNÉES PRODUITS WIN-MCP ===\n\n");

        if (productsData != null && productsData.containsKey("produits")) {
            List<Map<String, Object>> produits = (List<Map<String, Object>>) productsData.get("produits");
            sb.append("🏥 PRODUITS DISPONIBLES (").append(produits.size()).append(" produits):\n");

            double totalValue = 0.0;
            int totalStock = 0;
            int prescriptionRequired = 0;

            for (Map<String, Object> produit : produits) {
                sb.append("  • ").append(produit.get("designation"));
                sb.append(" (").append(produit.get("codePrd")).append(")");
                sb.append(" - Prix: ").append(produit.get("prixVenteStd")).append(" DH");
                sb.append(" - Stock: ").append(produit.get("totalStock"));
                sb.append(" - Prescription: ").append(produit.get("estOblgPrescription").equals(true) ? "Oui" : "Non").append("\n");

                // Calculate totals
                if (produit.get("prixVenteStd") != null) {
                    totalValue += Double.parseDouble(produit.get("prixVenteStd").toString());
                }
                if (produit.get("totalStock") != null) {
                    String stockStr = produit.get("totalStock").toString();
                    totalStock += (int) Double.parseDouble(stockStr);
                }
                if (produit.get("estOblgPrescription") != null && produit.get("estOblgPrescription").equals(true)) {
                    prescriptionRequired++;
                }
            }

            sb.append("  📊 TOTAUX PRODUITS:\n");
            sb.append("    - Valeur totale des prix: ").append(String.format("%.2f", totalValue)).append(" DH\n");
            sb.append("    - Stock total: ").append(totalStock).append("\n");
            sb.append("    - Prix moyen: ").append(String.format("%.2f", totalValue / produits.size())).append(" DH\n");
            sb.append("    - Produits sur prescription: ").append(prescriptionRequired).append("/").append(produits.size()).append("\n");
        } else {
            sb.append("❌ Aucune donnée produit trouvée\n");
        }

        return sb.toString();
    }

    private String formatPurchaseData(Map<String, Object> suppliersData, Map<String, Object> purchaseStats, String username) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 🛒 DONNÉES ACHATS WIN-MCP ===\n\n");

        // Suppliers data
        if (suppliersData != null && suppliersData.containsKey("fournisseurs")) {
            List<Map<String, Object>> fournisseurs = (List<Map<String, Object>>) suppliersData.get("fournisseurs");
            sb.append("🏭 FOURNISSEURS (").append(fournisseurs.size()).append(" fournisseurs):\n");

            double totalSolde = 0.0;
            int activeFournisseurs = 0;
            int laboratoires = 0;

            for (Map<String, Object> fournisseur : fournisseurs) {
                sb.append("  • ").append(fournisseur.get("raisonSociale"));
                sb.append(" (").append(fournisseur.get("codeFournisseur")).append(")");
                sb.append(" - Email: ").append(fournisseur.get("email"));
                sb.append(" - Ville: ").append(fournisseur.get("ville"));
                sb.append(" - Solde: ").append(fournisseur.get("soldeFournisseur")).append(" DH");
                sb.append(" - Statut: ").append(fournisseur.get("estActif").equals(true) ? "Actif" : "Inactif").append("\n");

                // Calculate totals
                if (fournisseur.get("soldeFournisseur") != null) {
                    totalSolde += Double.parseDouble(fournisseur.get("soldeFournisseur").toString());
                }
                if (fournisseur.get("estActif") != null && fournisseur.get("estActif").equals(true)) {
                    activeFournisseurs++;
                }
                if (fournisseur.get("estLaboratoire") != null && fournisseur.get("estLaboratoire").equals(true)) {
                    laboratoires++;
                }
            }

            sb.append("  📊 TOTAUX FOURNISSEURS:\n");
            sb.append("    - Solde total: ").append(String.format("%.2f", totalSolde)).append(" DH\n");
            sb.append("    - Fournisseurs actifs: ").append(activeFournisseurs).append("/").append(fournisseurs.size()).append("\n");
            sb.append("    - Laboratoires: ").append(laboratoires).append("/").append(fournisseurs.size()).append("\n");
        }

        // Purchase statistics
        if (purchaseStats != null && purchaseStats.containsKey("achats")) {
            List<Map<String, Object>> achats = (List<Map<String, Object>>) purchaseStats.get("achats");
            sb.append("\n💳 ACHATS RÉCENTS (").append(achats.size()).append(" achats):\n");

            double totalAchats = 0.0;
            for (Map<String, Object> achat : achats) {
                sb.append("  • Facture #").append(achat.get("numFacture"));
                sb.append(" - Date: ").append(achat.get("dateFacture"));
                sb.append(" - Montant: ").append(achat.get("montantTtc")).append(" DH\n");

                if (achat.get("montantTtc") != null) {
                    totalAchats += Double.parseDouble(achat.get("montantTtc").toString());
                }
            }

            sb.append("  📊 TOTAL ACHATS: ").append(String.format("%.2f", totalAchats)).append(" DH\n");
        }

        return sb.toString();
    }
}
