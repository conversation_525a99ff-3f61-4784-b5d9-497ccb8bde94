#!/usr/bin/env python3

import requests
import json

def test_chat_mcp_auth():
    """Test authentication with chat-mcp and endpoint access"""
    
    base_url = "http://localhost:8080/api"
    
    # Step 1: Authenticate
    print("🔐 Testing authentication...")
    auth_data = {
        "username": "user1",
        "password": "password"
    }
    
    try:
        auth_response = requests.post(f"{base_url}/auth/login", json=auth_data)
        print(f"Auth Status: {auth_response.status_code}")
        
        if auth_response.status_code == 200:
            auth_result = auth_response.json()
            token = auth_result.get('token')
            print(f"✅ Authentication successful!")
            print(f"Token: {token[:50]}...")
            
            # Step 2: Test endpoints
            headers = {"Authorization": f"Bearer {token}"}
            
            endpoints = [
                "/users/data",
                "/real-data/profile", 
                "/real-data/transactions",
                "/real-data/invoices",
                "/real-data/orders"
            ]
            
            for endpoint in endpoints:
                print(f"\n📡 Testing {endpoint}...")
                try:
                    response = requests.get(f"{base_url}{endpoint}", headers=headers)
                    print(f"Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        if isinstance(data, list):
                            print(f"✅ Success: {len(data)} items")
                        elif isinstance(data, dict):
                            print(f"✅ Success: {len(data)} fields")
                        else:
                            print(f"✅ Success: {type(data)}")
                    else:
                        print(f"❌ Error: {response.text}")
                        
                except Exception as e:
                    print(f"❌ Exception: {e}")
        else:
            print(f"❌ Authentication failed: {auth_response.text}")
            
    except Exception as e:
        print(f"❌ Authentication exception: {e}")

if __name__ == "__main__":
    test_chat_mcp_auth()
