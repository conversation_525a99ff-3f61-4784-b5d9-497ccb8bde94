<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
</head>
<body>
    <h1>WebSocket Test</h1>
    <div>
        <button id="connect">Connect</button>
        <button id="disconnect" disabled>Disconnect</button>
    </div>
    <div>
        <p>Conversation ID: <input id="conversationId" type="text" value="1"></p>
    </div>
    <div>
        <p>Messages:</p>
        <pre id="messages"></pre>
    </div>

    <script>
        let stompClient = null;
        const connectButton = document.getElementById('connect');
        const disconnectButton = document.getElementById('disconnect');
        const conversationIdInput = document.getElementById('conversationId');
        const messagesDisplay = document.getElementById('messages');

        connectButton.addEventListener('click', connect);
        disconnectButton.addEventListener('click', disconnect);

        function connect() {
            const socket = new SockJS('http://***************:8080/api/ws');
            stompClient = Stomp.over(socket);
            
            stompClient.connect({}, frame => {
                console.log('Connected: ' + frame);
                
                const conversationId = conversationIdInput.value;
                stompClient.subscribe(`/topic/conversation/${conversationId}`, message => {
                    const messageData = JSON.parse(message.body);
                    messagesDisplay.textContent += JSON.stringify(messageData, null, 2) + '\n\n';
                });
                
                connectButton.disabled = true;
                disconnectButton.disabled = false;
            }, error => {
                console.error('Error connecting to WebSocket: ', error);
            });
        }

        function disconnect() {
            if (stompClient !== null) {
                stompClient.disconnect();
                stompClient = null;
                
                connectButton.disabled = false;
                disconnectButton.disabled = true;
                
                console.log('Disconnected');
            }
        }
    </script>
</body>
</html>
