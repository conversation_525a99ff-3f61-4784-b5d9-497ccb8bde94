#!/usr/bin/env python3
"""
Test script for the new Knowledge Toggle functionality
Tests both internal (database) and general knowledge modes
"""

import requests
import json
import time

def test_knowledge_toggle():
    """Test the new knowledge toggle functionality"""
    
    base_url = "http://localhost:8081"
    
    # Test cases for internal mode (database)
    internal_tests = [
        {
            "content": "mes factures",
            "knowledgeMode": "internal",
            "description": "Test internal mode with invoice question"
        },
        {
            "content": "quel est mon nom",
            "knowledgeMode": "internal", 
            "description": "Test internal mode with profile question"
        },
        {
            "content": "mes transactions",
            "knowledgeMode": "internal",
            "description": "Test internal mode with transaction question"
        }
    ]
    
    # Test cases for general mode
    general_tests = [
        {
            "content": "capitale du maroc",
            "knowledgeMode": "general",
            "description": "Test general mode with geography question"
        },
        {
            "content": "qui est le président de la france",
            "knowledgeMode": "general",
            "description": "Test general mode with politics question"
        },
        {
            "content": "comment faire du café",
            "knowledgeMode": "general",
            "description": "Test general mode with general knowledge question"
        }
    ]
    
    # Test greeting (should work regardless of mode)
    greeting_tests = [
        {
            "content": "bonjour",
            "knowledgeMode": "internal",
            "description": "Test greeting with internal mode"
        },
        {
            "content": "salut",
            "knowledgeMode": "general", 
            "description": "Test greeting with general mode"
        }
    ]
    
    print("🧪 TESTING KNOWLEDGE TOGGLE FUNCTIONALITY")
    print("=" * 50)
    
    # Test internal mode
    print("\n🔍 TESTING INTERNAL MODE (Database)")
    print("-" * 30)
    for test in internal_tests:
        run_test(base_url, test)
        time.sleep(1)
    
    # Test general mode
    print("\n🌍 TESTING GENERAL MODE (AI Knowledge)")
    print("-" * 30)
    for test in general_tests:
        run_test(base_url, test)
        time.sleep(1)
    
    # Test greetings
    print("\n👋 TESTING GREETINGS")
    print("-" * 30)
    for test in greeting_tests:
        run_test(base_url, test)
        time.sleep(1)

def run_test(base_url, test):
    """Run a single test case"""
    
    payload = {
        "conversationId": f"test-{int(time.time())}",
        "content": test["content"],
        "username": "user1",
        "backend": "win-mcp",
        "knowledgeMode": test["knowledgeMode"]
    }
    
    print(f"\n📝 {test['description']}")
    print(f"   Question: '{test['content']}'")
    print(f"   Mode: {test['knowledgeMode']}")
    
    try:
        response = requests.post(
            f"{base_url}/api/chat",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('content', 'No content')
            print(f"   Response: {content[:100]}...")
            
            # Check if response indicates the correct mode was used
            if test['knowledgeMode'] == 'internal':
                if 'base de données' in content.lower() or 'données' in content.lower():
                    print("   ✅ Correctly used internal mode")
                elif 'erreur' in content.lower():
                    print("   ⚠️  Database error (expected if backends not running)")
                else:
                    print("   ❓ Response unclear about mode")
            else:  # general mode
                if 'connaissances générales' in content.lower() or 'base de données' not in content.lower():
                    print("   ✅ Correctly used general mode")
                else:
                    print("   ❓ Response unclear about mode")
        else:
            print(f"   ❌ Error: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Exception: {str(e)}")

def test_backend_health():
    """Test if backend services are running"""
    
    services = [
        {"name": "MCP Microservice", "url": "http://localhost:8081/actuator/health"},
        {"name": "Chat-MCP", "url": "http://localhost:8080/actuator/health"},
        {"name": "Win-MCP", "url": "http://localhost:4200/actuator/health"}
    ]
    
    print("\n🏥 CHECKING BACKEND SERVICES")
    print("-" * 30)
    
    for service in services:
        try:
            response = requests.get(service["url"], timeout=5)
            if response.status_code == 200:
                print(f"✅ {service['name']}: Running")
            else:
                print(f"⚠️  {service['name']}: Status {response.status_code}")
        except Exception as e:
            print(f"❌ {service['name']}: Not accessible ({str(e)})")

if __name__ == "__main__":
    test_backend_health()
    test_knowledge_toggle()
    
    print("\n" + "=" * 50)
    print("🎯 TEST SUMMARY:")
    print("- If you see 'Database error' for internal mode, start the backend services")
    print("- If general mode works, the toggle functionality is working correctly")
    print("- Check MCP microservice logs for detailed debugging information")
