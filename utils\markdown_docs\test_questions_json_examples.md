# 🧪 JSON Test Examples for MCP System

This file contains ready-to-use JSON examples for testing the MCP system via API calls.

## 🚀 Quick Test Commands

### PowerShell Commands (Windows)
```powershell
# Test personal information
Invoke-RestMethod -Uri 'http://localhost:8081/api/chat' -Method POST -ContentType 'application/json' -Body '{"content": "Quel est mon nom?", "conversationId": "test123", "username": "user1"}'

# Choose database option
Invoke-RestMethod -Uri 'http://localhost:8081/api/chat' -Method POST -ContentType 'application/json' -Body '{"content": "1", "conversationId": "test123", "username": "user1"}'
```

### cURL Commands (Linux/Mac)
```bash
# Test personal information
curl -X POST http://localhost:8081/api/chat \
  -H "Content-Type: application/json" \
  -d '{"content": "Quel est mon nom?", "conversationId": "test123", "username": "user1"}'

# Choose database option
curl -X POST http://localhost:8081/api/chat \
  -H "Content-Type: application/json" \
  -d '{"content": "1", "conversationId": "test123", "username": "user1"}'
```

---

## 📋 **PERSONAL INFORMATION TESTS**

### Test 1: Name Questions
```json
{"content": "Quel est mon nom?", "conversationId": "test001", "username": "user1"}
{"content": "1", "conversationId": "test001", "username": "user1"}
```

```json
{"content": "Comment je m'appelle?", "conversationId": "test002", "username": "user1"}
{"content": "1", "conversationId": "test002", "username": "user1"}
```

### Test 2: Contact Information
```json
{"content": "Quelle est mon adresse email?", "conversationId": "test003", "username": "user1"}
{"content": "1", "conversationId": "test003", "username": "user1"}
```

```json
{"content": "Où j'habite?", "conversationId": "test004", "username": "user1"}
{"content": "1", "conversationId": "test004", "username": "user1"}
```

### Test 3: Professional Information
```json
{"content": "Où je travaille?", "conversationId": "test005", "username": "user1"}
{"content": "1", "conversationId": "test005", "username": "user1"}
```

```json
{"content": "Quel est mon métier?", "conversationId": "test006", "username": "user1"}
{"content": "1", "conversationId": "test006", "username": "user1"}
```

---

## 💰 **FINANCIAL TESTS**

### Test 4: Balance Questions
```json
{"content": "Quel est mon solde?", "conversationId": "test007", "username": "user1"}
{"content": "1", "conversationId": "test007", "username": "user1"}
```

```json
{"content": "Combien j'ai sur mon compte?", "conversationId": "test008", "username": "user1"}
{"content": "1", "conversationId": "test008", "username": "user1"}
```

### Test 5: Credit Information
```json
{"content": "Quelle est ma limite de crédit?", "conversationId": "test009", "username": "user1"}
{"content": "1", "conversationId": "test009", "username": "user1"}
```

### Test 6: Payment Information
```json
{"content": "Quelle est ma méthode de paiement?", "conversationId": "test010", "username": "user1"}
{"content": "1", "conversationId": "test010", "username": "user1"}
```

```json
{"content": "Quand expire ma carte?", "conversationId": "test011", "username": "user1"}
{"content": "1", "conversationId": "test011", "username": "user1"}
```

---

## 📄 **SUBSCRIPTION TESTS**

### Test 7: Subscription Status
```json
{"content": "Quel est mon statut d'abonnement?", "conversationId": "test012", "username": "user1"}
{"content": "1", "conversationId": "test012", "username": "user1"}
```

### Test 8: Subscription Details
```json
{"content": "Combien coûte mon abonnement?", "conversationId": "test013", "username": "user1"}
{"content": "1", "conversationId": "test013", "username": "user1"}
```

```json
{"content": "Quand se termine mon abonnement?", "conversationId": "test014", "username": "user1"}
{"content": "1", "conversationId": "test014", "username": "user1"}
```

---

## 🧾 **TRANSACTION TESTS**

### Test 9: Recent Transactions
```json
{"content": "Quelles sont mes dernières transactions?", "conversationId": "test015", "username": "user1"}
{"content": "1", "conversationId": "test015", "username": "user1"}
```

### Test 10: Transaction Summary
```json
{"content": "Combien j'ai dépensé?", "conversationId": "test016", "username": "user1"}
{"content": "1", "conversationId": "test016", "username": "user1"}
```

---

## 📋 **INVOICE TESTS**

### Test 11: Recent Invoices
```json
{"content": "Quelles sont mes factures?", "conversationId": "test017", "username": "user1"}
{"content": "1", "conversationId": "test017", "username": "user1"}
```

### Test 12: Invoice Summary
```json
{"content": "Combien je dois?", "conversationId": "test018", "username": "user1"}
{"content": "1", "conversationId": "test018", "username": "user1"}
```

---

## 🛒 **ORDER TESTS**

### Test 13: Recent Orders
```json
{"content": "Quelles sont mes commandes?", "conversationId": "test019", "username": "user1"}
{"content": "1", "conversationId": "test019", "username": "user1"}
```

### Test 14: Order Summary
```json
{"content": "Combien de commandes j'ai?", "conversationId": "test020", "username": "user1"}
{"content": "1", "conversationId": "test020", "username": "user1"}
```

---

## 🔍 **COMPLEX TESTS**

### Test 15: Complete Profile
```json
{"content": "Résumé de mon compte?", "conversationId": "test021", "username": "user1"}
{"content": "1", "conversationId": "test021", "username": "user1"}
```

### Test 16: Account Statistics
```json
{"content": "Mon profil est-il complet?", "conversationId": "test022", "username": "user1"}
{"content": "1", "conversationId": "test022", "username": "user1"}
```

---

## 🎯 **FRIENDLY MESSAGE TESTS**

### Test 17: Greetings (Should not ask for source)
```json
{"content": "Bonjour", "conversationId": "test023", "username": "user1"}
```

```json
{"content": "Salut", "conversationId": "test024", "username": "user1"}
```

```json
{"content": "Hello", "conversationId": "test025", "username": "user1"}
```

### Test 18: General Questions (Should ask for source)
```json
{"content": "Comment ça va?", "conversationId": "test026", "username": "user1"}
{"content": "2", "conversationId": "test026", "username": "user1"}
```

---

## 📊 **EXPECTED RESPONSES**

### Real Data Responses (Option 1)
- **Name**: "Test User1"
- **Balance**: "1250.0 €"
- **Company**: "Pharmacie Centrale"
- **Job Title**: "Pharmacien"
- **Address**: "123 Rue de la Santé"
- **Email**: "<EMAIL>"
- **Subscription**: "Active"

### General Knowledge Responses (Option 2)
- AI will use general knowledge instead of personal data
- No access to user's personal information

---

## 🔧 **TESTING SCRIPT**

Save this as `test_all_questions.ps1`:

```powershell
# Test script for all MCP questions
$baseUrl = "http://localhost:8081/api/chat"
$headers = @{"Content-Type" = "application/json"}

# Test 1: Name
$body1 = '{"content": "Quel est mon nom?", "conversationId": "test001", "username": "user1"}'
$response1 = Invoke-RestMethod -Uri $baseUrl -Method POST -Headers $headers -Body $body1
Write-Host "Question: Quel est mon nom?"
Write-Host "Response: $($response1.content)"

$body1b = '{"content": "1", "conversationId": "test001", "username": "user1"}'
$response1b = Invoke-RestMethod -Uri $baseUrl -Method POST -Headers $headers -Body $body1b
Write-Host "Answer: $($response1b.content)"
Write-Host "---"

# Add more tests here...
```

---

**🎉 Use these examples to thoroughly test the real database integration!**
