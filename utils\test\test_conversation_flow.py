#!/usr/bin/env python3
"""
Test the exact conversation flow that should happen
"""

import requests
import json
import time

def test_exact_conversation_flow():
    """Test the exact conversation flow with same conversation ID"""
    print("🔍 TESTING EXACT CONVERSATION FLOW")
    print("=" * 50)
    
    # Use a fixed conversation ID to simulate what <PERSON><PERSON> should do
    conversation_id = f"angular-test-{int(time.time())}"
    username = "user1"
    
    print(f"🧪 Testing conversation flow:")
    print(f"   Conversation ID: {conversation_id}")
    print(f"   Username: {username}")
    
    # Step 1: Send medical question
    print(f"\n📤 Step 1: Sending medical question...")
    try:
        response1 = requests.post(
            "http://localhost:8081/api/chat",
            json={
                "conversationId": conversation_id,
                "content": "quel est mon groupe sanguin?",
                "username": username
            },
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"   Status: {response1.status_code}")
        if response1.status_code == 200:
            data1 = response1.json()
            content1 = data1.get('content', '')
            print(f"   Response: {content1[:100]}...")
            
            # Check if we get the source selection prompt
            if "Souhaitez-vous que je vous réponde" in content1:
                print(f"   ✅ Got source selection prompt")
                
                # Step 2: Send option "1" with SAME conversation ID
                print(f"\n📤 Step 2: Selecting database option (1)...")
                time.sleep(1)  # Small delay to simulate user interaction
                
                response2 = requests.post(
                    "http://localhost:8081/api/chat",
                    json={
                        "conversationId": conversation_id,  # SAME ID!
                        "content": "1",
                        "username": username
                    },
                    headers={'Content-Type': 'application/json'}
                )
                
                print(f"   Status: {response2.status_code}")
                if response2.status_code == 200:
                    data2 = response2.json()
                    content2 = data2.get('content', '')
                    print(f"   Response: {content2[:200]}...")
                    
                    # Check for real medical data
                    if "o+" in content2.lower() and "groupe sanguin" in content2.lower():
                        print(f"   🎉 SUCCESS: Got real medical data!")
                        print(f"   ✅ Conversation flow working correctly")
                        return True
                    elif "je n'ai pas trouvé" in content2.lower():
                        print(f"   ❌ FAILED: Still getting 'info not found'")
                        print(f"   🔍 This suggests username or authentication issue")
                        return False
                    else:
                        print(f"   ⚠️  UNCLEAR: Unexpected response")
                        return False
                else:
                    print(f"   ❌ Step 2 failed: {response2.text}")
                    return False
            else:
                print(f"   ❌ Didn't get source selection prompt")
                print(f"   Response was: {content1}")
                return False
        else:
            print(f"   ❌ Step 1 failed: {response1.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_different_conversation_ids():
    """Test what happens with different conversation IDs (wrong way)"""
    print(f"\n🚫 TESTING WITH DIFFERENT CONVERSATION IDS (WRONG WAY)")
    print("=" * 60)
    
    username = "user1"
    
    # Step 1: Send medical question with one conversation ID
    conversation_id_1 = f"test-wrong-1-{int(time.time())}"
    print(f"📤 Step 1: Sending question with conversation ID: {conversation_id_1}")
    
    try:
        response1 = requests.post(
            "http://localhost:8081/api/chat",
            json={
                "conversationId": conversation_id_1,
                "content": "quel est mon groupe sanguin?",
                "username": username
            },
            headers={'Content-Type': 'application/json'}
        )
        
        if response1.status_code == 200:
            data1 = response1.json()
            content1 = data1.get('content', '')
            
            if "Souhaitez-vous que je vous réponde" in content1:
                # Step 2: Send option "1" with DIFFERENT conversation ID
                conversation_id_2 = f"test-wrong-2-{int(time.time())}"
                print(f"📤 Step 2: Sending option with DIFFERENT conversation ID: {conversation_id_2}")
                
                response2 = requests.post(
                    "http://localhost:8081/api/chat",
                    json={
                        "conversationId": conversation_id_2,  # DIFFERENT ID!
                        "content": "1",
                        "username": username
                    },
                    headers={'Content-Type': 'application/json'}
                )
                
                if response2.status_code == 200:
                    data2 = response2.json()
                    content2 = data2.get('content', '')
                    print(f"   Response: {content2[:200]}...")
                    
                    if "Souhaitez-vous que je vous réponde" in content2:
                        print(f"   ❌ CONFIRMED: Different conversation IDs break the flow")
                        print(f"   🔍 MCP microservice asks for source selection again")
                        return False
                    else:
                        print(f"   ⚠️  Unexpected behavior with different conversation IDs")
                        return False
                        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    print("🧪 CONVERSATION FLOW TESTING")
    print("=" * 70)
    
    # Test correct conversation flow
    correct_flow = test_exact_conversation_flow()
    
    # Test incorrect conversation flow
    incorrect_flow = test_different_conversation_ids()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 CONVERSATION FLOW TEST RESULTS")
    print(f"=" * 70)
    
    if correct_flow:
        print(f"✅ Correct conversation flow (same ID): WORKING")
    else:
        print(f"❌ Correct conversation flow (same ID): FAILED")
    
    if not incorrect_flow:
        print(f"✅ Different conversation IDs: CORRECTLY BREAKS FLOW")
    else:
        print(f"❌ Different conversation IDs: UNEXPECTED BEHAVIOR")
    
    print(f"\n💡 CONCLUSION:")
    if correct_flow:
        print(f"✅ MCP microservice conversation management is working correctly")
        print(f"🔍 The issue is likely in Angular frontend conversation ID management")
        print(f"📋 Check browser console for conversation ID logs")
        print(f"🎯 Ensure Angular uses the SAME conversation ID for question and response")
    else:
        print(f"❌ There's an issue with the MCP microservice or backend authentication")
        print(f"🔍 Check MCP microservice logs for authentication errors")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"1. Open Angular frontend: http://localhost:4200")
    print(f"2. Login with Win-MCP using user1/password")
    print(f"3. Open browser console (F12)")
    print(f"4. Ask: 'quel est mon groupe sanguin?'")
    print(f"5. Check console logs for conversation ID consistency")
    print(f"6. When prompted, select option '1'")
    print(f"7. Check if same conversation ID is used for both requests")

if __name__ == "__main__":
    main()
