#!/usr/bin/env python3
"""
Test script for Win-MCP Fournisseur endpoints
Tests all the new supplier/fournisseur endpoints we just created
"""

import requests
import json

# Win-MCP configuration
WIN_MCP_BASE_URL = "http://localhost:8082"

# Authentication headers for Win-MCP (dual token system)
HEADERS = {
    "Content-Type": "application/json",
    "authorizationTenant": "Bearer tenant_token_placeholder",
    "Authorization": "Bearer user_token_placeholder"
}

def test_endpoint(endpoint_name, url, expected_status=200):
    """Test a single endpoint and return the result"""
    try:
        print(f"\n🔍 Testing {endpoint_name}...")
        print(f"   URL: {url}")
        
        response = requests.get(url, headers=HEADERS, timeout=10)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == expected_status:
            try:
                data = response.json()
                print(f"   ✅ SUCCESS: {endpoint_name}")
                
                # Show data structure
                if isinstance(data, dict):
                    if 'fournisseurs' in data:
                        fournisseurs = data['fournisseurs']
                        total_items = data.get('totalItems', len(fournisseurs))
                        print(f"   📊 Found {total_items} suppliers")
                        
                        # Show first supplier details
                        if fournisseurs and len(fournisseurs) > 0:
                            first_supplier = fournisseurs[0]
                            print(f"   📋 First supplier: {first_supplier.get('raisonSociale', 'N/A')} ({first_supplier.get('codeFournisseur', 'N/A')})")
                            print(f"       Email: {first_supplier.get('email', 'N/A')}")
                            print(f"       Ville: {first_supplier.get('ville', 'N/A')}")
                            print(f"       Actif: {first_supplier.get('estActif', 'N/A')}")
                            print(f"       Laboratoire: {first_supplier.get('estLaboratoire', 'N/A')}")
                    elif 'totalFournisseurs' in data:
                        # Statistics endpoint
                        print(f"   📊 Statistics:")
                        print(f"       Total suppliers: {data.get('totalFournisseurs', 'N/A')}")
                        print(f"       Active suppliers: {data.get('activeFournisseurs', 'N/A')}")
                        print(f"       Total balance: {data.get('totalSolde', 'N/A')} DH")
                    elif 'raisonSociale' in data:
                        # Single supplier endpoint
                        print(f"   📋 Supplier: {data.get('raisonSociale', 'N/A')} ({data.get('codeFournisseur', 'N/A')})")
                        print(f"       Email: {data.get('email', 'N/A')}")
                        print(f"       Ville: {data.get('ville', 'N/A')}")
                
                return True, data
                
            except json.JSONDecodeError:
                print(f"   ⚠️  Response is not valid JSON")
                print(f"   Response text: {response.text[:200]}...")
                return False, response.text
        else:
            print(f"   ❌ FAILED: Expected {expected_status}, got {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
            return False, response.text
            
    except requests.exceptions.RequestException as e:
        print(f"   ❌ REQUEST ERROR: {str(e)}")
        return False, str(e)

def main():
    print("🎯 TESTING WIN-MCP FOURNISSEUR ENDPOINTS")
    print("=" * 50)
    
    # Test endpoints
    endpoints = [
        ("All Suppliers", f"{WIN_MCP_BASE_URL}/api/winplus/fournisseurs"),
        ("Active Suppliers Only", f"{WIN_MCP_BASE_URL}/api/winplus/fournisseurs?activeOnly=true"),
        ("Suppliers with Pagination", f"{WIN_MCP_BASE_URL}/api/winplus/fournisseurs?page=0&size=5"),
        ("Search Suppliers", f"{WIN_MCP_BASE_URL}/api/winplus/fournisseurs?search=Laboratoires"),
        ("Supplier by ID", f"{WIN_MCP_BASE_URL}/api/winplus/fournisseurs/1"),
        ("Supplier by Code", f"{WIN_MCP_BASE_URL}/api/winplus/fournisseurs/code/FOUR001"),
        ("Suppliers by City", f"{WIN_MCP_BASE_URL}/api/winplus/fournisseurs/ville/Casablanca"),
        ("Laboratory Suppliers", f"{WIN_MCP_BASE_URL}/api/winplus/fournisseurs/laboratoires"),
        ("Supplier Statistics", f"{WIN_MCP_BASE_URL}/api/winplus/fournisseurs/statistics"),
    ]
    
    results = []
    
    for endpoint_name, url in endpoints:
        success, data = test_endpoint(endpoint_name, url)
        results.append((endpoint_name, success, data))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY RESULTS:")
    print("=" * 50)
    
    successful = 0
    failed = 0
    
    for endpoint_name, success, data in results:
        if success:
            print(f"✅ {endpoint_name}")
            successful += 1
        else:
            print(f"❌ {endpoint_name}")
            failed += 1
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"   ✅ Successful: {successful}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📊 Success Rate: {(successful/(successful+failed)*100):.1f}%")
    
    if successful > 0:
        print(f"\n🎉 GREAT! {successful} fournisseur endpoints are working!")
        print("   The new supplier data type has been successfully added to Win-MCP!")
    else:
        print(f"\n⚠️  All endpoints failed. Check Win-MCP server status and authentication.")

if __name__ == "__main__":
    main()
