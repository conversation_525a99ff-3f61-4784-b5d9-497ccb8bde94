package com.chatbootmcp.chatmcp.service;

import com.chatbootmcp.chatmcp.entity.Fournisseur;
import com.chatbootmcp.chatmcp.repository.FournisseurRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
public class FournisseurService {
    
    @Autowired
    private FournisseurRepository fournisseurRepository;
    
    /**
     * Get all suppliers with pagination
     */
    public Map<String, Object> getAllFournisseurs(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("raisonSociale").ascending());
        Page<Fournisseur> fournisseurPage = fournisseurRepository.findAll(pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("fournisseurs", fournisseurPage.getContent());
        response.put("totalItems", fournisseurPage.getTotalElements());
        response.put("totalPages", fournisseurPage.getTotalPages());
        response.put("currentPage", page);
        response.put("pageSize", size);
        
        return response;
    }
    
    /**
     * Get active suppliers only
     */
    public Map<String, Object> getActiveFournisseurs(int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("raisonSociale").ascending());
        Page<Fournisseur> fournisseurPage = fournisseurRepository.findByEstActifTrue(pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("fournisseurs", fournisseurPage.getContent());
        response.put("totalItems", fournisseurPage.getTotalElements());
        response.put("totalPages", fournisseurPage.getTotalPages());
        response.put("currentPage", page);
        response.put("pageSize", size);
        
        return response;
    }
    
    /**
     * Get suppliers by city
     */
    public Map<String, Object> getFournisseursByVille(String ville, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Fournisseur> fournisseurPage = fournisseurRepository.findByVilleIgnoreCase(ville, pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("fournisseurs", fournisseurPage.getContent());
        response.put("totalItems", fournisseurPage.getTotalElements());
        response.put("totalPages", fournisseurPage.getTotalPages());
        response.put("currentPage", page);
        response.put("ville", ville);
        
        return response;
    }
    
    /**
     * Get laboratory suppliers only
     */
    public Map<String, Object> getLaboratoires(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Fournisseur> fournisseurPage = fournisseurRepository.findByEstLaboratoireTrue(pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("fournisseurs", fournisseurPage.getContent());
        response.put("totalItems", fournisseurPage.getTotalElements());
        response.put("totalPages", fournisseurPage.getTotalPages());
        response.put("currentPage", page);
        response.put("type", "laboratoires");
        
        return response;
    }
    
    /**
     * Get supplier by code
     */
    public Optional<Fournisseur> getFournisseurByCode(String codeFournisseur) {
        return fournisseurRepository.findByCodeFournisseur(codeFournisseur);
    }
    
    /**
     * Get suppliers statistics
     */
    public Map<String, Object> getFournisseursStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        long totalFournisseurs = fournisseurRepository.count();
        long activeFournisseurs = fournisseurRepository.countActiveFournisseurs();
        Double totalSolde = fournisseurRepository.getTotalSoldeActifs();
        
        stats.put("totalFournisseurs", totalFournisseurs);
        stats.put("activeFournisseurs", activeFournisseurs);
        stats.put("inactiveFournisseurs", totalFournisseurs - activeFournisseurs);
        stats.put("totalSolde", totalSolde != null ? totalSolde : 0.0);
        stats.put("pourcentageActifs", totalFournisseurs > 0 ? (activeFournisseurs * 100.0 / totalFournisseurs) : 0.0);
        
        return stats;
    }
    
    /**
     * Search suppliers by name
     */
    public Map<String, Object> searchFournisseurs(String searchTerm, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        Page<Fournisseur> fournisseurPage = fournisseurRepository.findByRaisonSocialeContainingIgnoreCase(searchTerm, pageable);
        
        Map<String, Object> response = new HashMap<>();
        response.put("fournisseurs", fournisseurPage.getContent());
        response.put("totalItems", fournisseurPage.getTotalElements());
        response.put("totalPages", fournisseurPage.getTotalPages());
        response.put("currentPage", page);
        response.put("searchTerm", searchTerm);
        
        return response;
    }
}
