package com.example.mcp_microservice_chatboot_ai.service;

import com.example.mcp_microservice_chatboot_ai.model.ChatMessage;
import com.example.mcp_microservice_chatboot_ai.model.ChatMessage.MessageRole;
import com.example.mcp_microservice_chatboot_ai.model.dto.ChatRequest;
import com.example.mcp_microservice_chatboot_ai.model.dto.ChatResponse;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.service.OpenAiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Refactored AI Chat Service - Business Logic Only
 *
 * This service handles the core AI chat functionality and conversation flow.
 * Data communication is delegated to specialized tool function services:
 * - ChatMcpToolsFunctions: for chat-mcp backend communication
 * - WinMcpToolsFunctions: for win-mcp (WinPlus) backend communication
 */
@Service
public class AiChatService {

    private final OpenAiService openAiService;
    private final ChatMcpToolsFunctions chatMcpTools;
    private final WinMcpToolsFunctions winMcpTools;
    private final WinMcpSpecificToolsFunctions winMcpSpecificTools;

    @Value("${openai.model}")
    private String model;

    @Value("${openai.temperature}")
    private float temperature;

    @Value("${openai.max-tokens}")
    private int maxTokens;

    @Value("${mcp.backend.type}")
    private String backendType;

    @Value("${mcp.tool.strategy:SPECIFIC}")
    private String toolStrategy;

    @Autowired
    public AiChatService(OpenAiService openAiService,
                        ChatMcpToolsFunctions chatMcpTools,
                        WinMcpToolsFunctions winMcpTools,
                        WinMcpSpecificToolsFunctions winMcpSpecificTools) {
        this.openAiService = openAiService;
        this.chatMcpTools = chatMcpTools;
        this.winMcpTools = winMcpTools;
        this.winMcpSpecificTools = winMcpSpecificTools;
    }



    /**
     * Processes a chat request and generates a response using the AI model.
     * Now supports both chat-mcp and win-mcp backends.
     *
     * @param chatRequest The chat request
     * @return A Mono containing the chat response
     */
    public Mono<ChatResponse> processChat(ChatRequest chatRequest) {
        String userMessage = chatRequest.getContent();
        String conversationId = chatRequest.getConversationId();
        String username = chatRequest.getUsername();
        String knowledgeMode = chatRequest.getKnowledgeMode();

        // Default to internal mode if not specified
        if (knowledgeMode == null || knowledgeMode.trim().isEmpty()) {
            knowledgeMode = "internal";
        }

        // Create the system prompt
        String systemPromptText = createSystemPrompt();

        // Debug logging
        System.out.println("Processing chat: conversationId=" + conversationId + ", knowledgeMode=" + knowledgeMode + ", message=" + userMessage);

        // Check if this is a greeting message
        if (isGreeting(userMessage.toLowerCase().trim())) {
            System.out.println("Detected greeting message: " + userMessage);
            return generateGreetingResponse(userMessage, conversationId, username, systemPromptText);
        }

        // Route based on knowledge mode
        if ("general".equals(knowledgeMode)) {
            System.out.println("Using general knowledge for question: " + userMessage);
            return generateGeneralKnowledgeResponse(userMessage, conversationId, username, systemPromptText);
        } else {
            // Use database/internal knowledge
            String selectedBackend = getSelectedBackend(chatRequest);
            System.out.println("Using database source for question: " + userMessage + " using backend: " + selectedBackend);
            return generateDatabaseResponse(userMessage, conversationId, username, systemPromptText, selectedBackend);
        }
    }

    /**
     * Creates the system prompt for the AI.
     *
     * @return The system prompt template
     */
    private String createSystemPrompt() {
        String currentBackend = getConfiguredBackend();
        String backendName = "WIN_MCP".equals(backendType) ? "WinPlus-MCP (système de simulation réel)" : "Chat-MCP (système de démonstration)";

        return """
                Tu es un assistant intelligent pour une application de gestion de pharmacie appelée WinPlusPharma.
                Tu réponds toujours en français, de manière professionnelle, claire et concise.

                🎯 Ton rôle est d'aider l'utilisateur à retrouver des informations depuis le système : """ + backendName + """

                ---

                🧠 Si l'utilisateur envoie un message **amical ou introductif** (ex. : "Bonjour", "Salut", "Hi", "Comment ça va ?"),
                réponds simplement de façon amicale et naturelle.

                ---

                📂 Pour les questions nécessitant des données de la base de données :
                - Utilise uniquement les données du système configuré
                - Si aucune information n'est trouvée, réponds : "Je n'ai pas trouvé la réponse dans la base de données."

                🌍 Pour les questions de connaissances générales :
                - Utilise uniquement ton savoir GPT
                - Ne fais aucune référence aux bases de données

                🚫 Ne combine jamais les deux sources dans une même réponse.
                """;
    }



    /**
     * Gets the configured backend from application.properties
     */
    private String getConfiguredBackend() {
        if ("WIN_MCP".equals(backendType)) {
            return "win-mcp";
        } else {
            return "chat-mcp"; // default
        }
    }

    /**
     * Gets the selected backend from the request, falling back to configured backend
     */
    private String getSelectedBackend(ChatRequest chatRequest) {
        if (chatRequest.getBackend() != null && !chatRequest.getBackend().trim().isEmpty()) {
            String requestedBackend = chatRequest.getBackend().trim().toLowerCase();
            if ("chat-mcp".equals(requestedBackend) || "win-mcp".equals(requestedBackend)) {
                System.out.println("🔄 Using backend from request: " + requestedBackend);
                return requestedBackend;
            }
        }

        // Fallback to configured backend
        String configuredBackend = getConfiguredBackend();
        System.out.println("🔄 Using configured backend: " + configuredBackend);
        return configuredBackend;
    }

    /**
     * Checks if a message is a common greeting.
     *
     * @param message The message to check (lowercase)
     * @return True if the message is a greeting, false otherwise
     */
    private boolean isGreeting(String message) {
        // Common greetings in English and French
        String[] greetings = {
            // English greetings
            "hello", "hi", "hey", "how are you", "how's it going", "what's up", "good morning", "good afternoon", "good evening",
            "i'm fine", "i am fine", "i'm good", "i am good", "i'm great", "i am great", "fine thanks", "good thanks",
            "thank you", "thanks", "nice to meet you", "pleased to meet you", "good to see you",

            // French greetings
            "bonjour", "salut", "coucou", "comment ça va", "comment vas-tu", "ça va", "bonsoir", "je vais bien",
            "je suis bien", "bien merci", "merci", "enchanté", "ravi de vous rencontrer", "content de vous voir"
        };

        for (String greeting : greetings) {
            if (message.equals(greeting) ||
                message.startsWith(greeting + " ") ||
                message.endsWith(" " + greeting) ||
                message.contains(" " + greeting + " ")) {
                return true;
            }
        }

        // Check for common greeting patterns
        if (message.contains("i'm") && (message.contains("fine") || message.contains("good") || message.contains("great"))) {
            return true;
        }

        if (message.contains("je suis") && (message.contains("bien") || message.contains("bon"))) {
            return true;
        }

        return false;
    }

    /**
     * Generates a response for greeting messages.
     */
    private Mono<ChatResponse> generateGreetingResponse(String userMessage, String conversationId,
                                                       String username, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user", userMessage));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature((double) temperature)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);

        // Extract the response content
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        // Create a chat message from the response
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(responseContent)
                .timestamp(LocalDateTime.now())
                .build();

        // Return the chat response
        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }



    /**
     * Generates a response using the database (Reactive version).
     */
    private Mono<ChatResponse> generateDatabaseResponse(String userMessage, String conversationId,
                                                      String username, String systemMessage, String selectedBackend) {
        try {
            System.out.println("🔍 GENERATE DATABASE RESPONSE: Starting for backend: " + selectedBackend);
            System.out.println("🔍 GENERATE DATABASE RESPONSE: Username: " + username + ", Message: " + userMessage);

            if ("win-mcp".equals(selectedBackend)) {
                // Use WinPlus Smart MCP tools
                System.out.println("🏥 Using Win-MCP backend for data retrieval");
                return getWinMcpDataReactive(username, userMessage)
                        .doOnNext(userData -> {
                            System.out.println("✅ Win-MCP data retrieved successfully. Length: " + userData.length());
                            System.out.println("📊 Win-MCP data preview: " + userData.substring(0, Math.min(200, userData.length())) + "...");
                        })
                        .flatMap(userData -> generateOpenAIResponseReactive(userMessage, conversationId, userData, systemMessage))
                        .onErrorResume(e -> {
                            System.out.println("❌ Error in generateDatabaseResponse (WinPlus): " + e.getMessage());
                            e.printStackTrace();
                            String errorMessage = "Erreur lors de la récupération des données depuis WinPlus: " + e.getMessage();
                            return createChatResponse(conversationId, errorMessage);
                        });
            } else {
                // Use Chat-MCP Smart tools (reactive)
                System.out.println("💬 Using Chat-MCP backend for data retrieval");
                return getChatMcpDataReactive(username, userMessage)
                        .doOnNext(userData -> {
                            System.out.println("✅ Chat-MCP data retrieved successfully. Length: " + userData.length());
                            System.out.println("📊 Chat-MCP data preview: " + userData.substring(0, Math.min(200, userData.length())) + "...");
                        })
                        .flatMap(userData -> generateOpenAIResponseReactive(userMessage, conversationId, userData, systemMessage))
                        .onErrorResume(e -> {
                            System.out.println("❌ Error in generateDatabaseResponse (Chat-MCP): " + e.getMessage());
                            e.printStackTrace();
                            String errorMessage = "Erreur lors de la récupération des données depuis Chat-MCP: " + e.getMessage();
                            return createChatResponse(conversationId, errorMessage);
                        });
            }

        } catch (Exception e) {
            System.out.println("Error in generateDatabaseResponse: " + e.getMessage());
            e.printStackTrace();
            String errorMessage = "Erreur lors de la récupération des données depuis " +
                                (selectedBackend.equals("win-mcp") ? "WinPlus" : "Chat-MCP") + ": " + e.getMessage();
            return createChatResponse(conversationId, errorMessage);
        }
    }



    /**
     * Gets data from Chat-MCP backend using SMART AI-powered tool selection (Reactive version)
     */
    private Mono<String> getChatMcpDataReactive(String username, String userMessage) {
        // Use AI to intelligently determine what data is needed and fetch ALL relevant data
        return chatMcpTools.getSmartChatMcpDataToolReactive()
                .apply(new com.example.mcp_microservice_chatboot_ai.model.SmartDataRequest(username, userMessage));
    }

    /**
     * Gets data from Win-MCP backend using configurable tool selection strategy
     */
    private Mono<String> getWinMcpDataReactive(String username, String userMessage) {
        if ("SPECIFIC".equals(toolStrategy)) {
            // Use SPECIFIC TOOLS approach - AI chooses the right endpoint based on question
            System.out.println("🎯 Using SPECIFIC TOOLS approach for Win-MCP");
            return getWinMcpDataWithSpecificTools(username, userMessage);
        } else {
            // Use SMART approach - fetch ALL data (legacy)
            System.out.println("🧠 Using SMART approach for Win-MCP (legacy)");
            return Mono.fromCallable(() ->
                winMcpTools.getSmartWinMcpDataTool()
                    .apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username))
            ).subscribeOn(reactor.core.scheduler.Schedulers.boundedElastic());
        }
    }

    /**
     * Gets data using SPECIFIC TOOLS approach - AI intelligently selects the right tool
     */
    private Mono<String> getWinMcpDataWithSpecificTools(String username, String userMessage) {
        return Mono.fromCallable(() -> {
            // Analyze the user message to determine which specific tool to use
            String question = userMessage.toLowerCase();

            if (containsKeywords(question, "client", "customer", "profil", "compte", "information personnelle")) {
                System.out.println("👥 Detected CLIENT question - using Client Data Tool");
                return winMcpSpecificTools.getWinMcpClientDataTool()
                    .apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
            }
            else if (containsKeywords(question, "vente", "sales", "chiffre", "revenue", "facture", "invoice", "transaction")) {
                System.out.println("💰 Detected SALES question - using Sales Data Tool");
                return winMcpSpecificTools.getWinMcpSalesDataTool()
                    .apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
            }
            else if (containsKeywords(question, "produit", "product", "stock", "inventory", "médicament", "medication")) {
                System.out.println("📦 Detected PRODUCT question - using Product Data Tool");
                return winMcpSpecificTools.getWinMcpProductDataTool()
                    .apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
            }
            else if (containsKeywords(question, "achat", "purchase", "fournisseur", "supplier", "procurement")) {
                System.out.println("🛒 Detected PURCHASE question - using Purchase Data Tool");
                return winMcpSpecificTools.getWinMcpPurchaseDataTool()
                    .apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
            }
            else {
                // Default to client data for general questions
                System.out.println("❓ General question - defaulting to Client Data Tool");
                return winMcpSpecificTools.getWinMcpClientDataTool()
                    .apply(new com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest(username));
            }
        }).subscribeOn(reactor.core.scheduler.Schedulers.boundedElastic());
    }

    /**
     * Helper method to check if question contains specific keywords
     */
    private boolean containsKeywords(String question, String... keywords) {
        for (String keyword : keywords) {
            if (question.contains(keyword)) {
                return true;
            }
        }
        return false;
    }



    /**
     * Creates a simple chat response
     */
    private Mono<ChatResponse> createChatResponse(String conversationId, String content) {
        ChatMessage responseMessage = ChatMessage.builder()
                .id(UUID.randomUUID().toString())
                .conversationId(conversationId)
                .role(MessageRole.ASSISTANT)
                .content(content)
                .timestamp(LocalDateTime.now())
                .build();

        return Mono.just(ChatResponse.fromChatMessage(responseMessage));
    }

    /**
     * Generates OpenAI response with user data (Reactive version)
     */
    private Mono<ChatResponse> generateOpenAIResponseReactive(String userMessage, String conversationId,
                                                             String userData, String systemMessage) {
        return Mono.fromCallable(() -> {
            // Create chat messages for OpenAI
            List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
            messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
            messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                    "L'utilisateur a demandé: " + userMessage + "\n\n" +
                    "Voici les informations disponibles dans la base de données:\n" + userData + "\n\n" +
                    "INSTRUCTIONS IMPORTANTES:\n" +
                    "1. Réponds en français en utilisant ces informations de la base de données.\n" +
                    "2. Analyse soigneusement toutes les données fournies pour répondre à la question.\n" +
                    "3. Si les données contiennent des informations pertinentes (même partielles), utilise-les pour construire une réponse utile.\n" +
                    "4. Sois précis et informatif en utilisant les données disponibles.\n" +
                    "5. Si vraiment aucune information pertinente n'est trouvée dans les données, alors seulement dis que l'information n'est pas disponible.\n" +
                    "6. Pour les questions générales (géographie, histoire, etc.), réponds: \"Cette information n'est pas disponible dans la base de données. Veuillez utiliser le mode 'Global' pour les connaissances générales.\"\n" +
                    "7. Ne demande JAMAIS à l'utilisateur de choisir entre les options 1 et 2."));

            // Create the chat completion request
            ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                    .model(model)
                    .messages(messages)
                    .temperature(0.0)
                    .maxTokens(maxTokens)
                    .build();

            // Call the OpenAI API
            ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);
            String responseContent = result.getChoices().get(0).getMessage().getContent();

            return responseContent;
        })
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(responseContent -> createChatResponse(conversationId, responseContent));
    }

    /**
     * Generates OpenAI response with user data (Blocking version - kept for compatibility)
     */
    private Mono<ChatResponse> generateOpenAIResponse(String userMessage, String conversationId,
                                                     String userData, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                "L'utilisateur a demandé: " + userMessage + "\n\n" +
                "Voici les informations disponibles dans la base de données:\n" + userData + "\n\n" +
                "INSTRUCTIONS IMPORTANTES:\n" +
                "1. Réponds en français en utilisant ces informations de la base de données.\n" +
                "2. Analyse soigneusement toutes les données fournies pour répondre à la question.\n" +
                "3. Si les données contiennent des informations pertinentes (même partielles), utilise-les pour construire une réponse utile.\n" +
                "4. Sois précis et informatif en utilisant les données disponibles.\n" +
                "5. Si vraiment aucune information pertinente n'est trouvée dans les données, alors seulement dis que l'information n'est pas disponible.\n" +
                "6. Pour les questions générales (géographie, histoire, etc.), réponds: \"Cette information n'est pas disponible dans la base de données. Veuillez utiliser le mode 'Global' pour les connaissances générales.\"\n" +
                "7. Ne demande JAMAIS à l'utilisateur de choisir entre les options 1 et 2."));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature(0.0)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        return createChatResponse(conversationId, responseContent);
    }

    /**
     * Generates a response using general knowledge.
     */
    private Mono<ChatResponse> generateGeneralKnowledgeResponse(String userMessage, String conversationId,
                                                              String username, String systemMessage) {
        // Create chat messages for OpenAI
        List<com.theokanning.openai.completion.chat.ChatMessage> messages = new ArrayList<>();
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("system", systemMessage));
        messages.add(new com.theokanning.openai.completion.chat.ChatMessage("user",
                "L'utilisateur a demandé: " + userMessage + "\n\n" +
                "Réponds en français en utilisant UNIQUEMENT tes connaissances générales. " +
                "Ne fais AUCUNE supposition à partir des bases de données."));

        // Create the chat completion request
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                .model(model)
                .messages(messages)
                .temperature((double) temperature)
                .maxTokens(maxTokens)
                .build();

        // Call the OpenAI API
        ChatCompletionResult result = openAiService.createChatCompletion(completionRequest);
        String responseContent = result.getChoices().get(0).getMessage().getContent();

        return createChatResponse(conversationId, responseContent);
    }
}
