#!/usr/bin/env python3
"""
Simple test for Chat-MCP endpoints
"""

import requests
import json

def test_chat_mcp_auth():
    """Test Chat-MCP authentication"""
    print("🔐 TESTING CHAT-MCP AUTHENTICATION")
    print("=" * 50)
    
    auth_url = "http://localhost:8080/api/auth/login"
    auth_data = {
        "username": "user1",
        "password": "password123"
    }
    
    try:
        response = requests.post(auth_url, json=auth_data)
        print(f"Auth Status: {response.status_code}")
        print(f"Auth Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            print(f"✅ Authentication successful")
            return token
        else:
            print(f"❌ Authentication failed")
            return None
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def test_chat_mcp_endpoints(token):
    """Test Chat-MCP endpoints directly"""
    print("\n📊 TESTING CHAT-MCP ENDPOINTS")
    print("=" * 50)
    
    if not token:
        print("❌ No token available")
        return
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    endpoints = [
        "/api/users/data",
        "/api/real-data/profile", 
        "/api/real-data/invoices",
        "/api/real-data/transactions",
        "/api/real-data/orders",
        "/api/real-data/financial-summary"
    ]
    
    for endpoint in endpoints:
        try:
            url = f"http://localhost:8080{endpoint}"
            response = requests.get(url, headers=headers)
            print(f"\n{endpoint}:")
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    print(f"  ✅ Success: {len(data)} items")
                elif isinstance(data, dict):
                    print(f"  ✅ Success: {len(data)} fields")
                    # Show some key fields
                    if 'username' in data:
                        print(f"  Username: {data.get('username')}")
                    if 'fullName' in data:
                        print(f"  Full Name: {data.get('fullName')}")
                    if 'email' in data:
                        print(f"  Email: {data.get('email')}")
            else:
                print(f"  ❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")

def test_mcp_microservice():
    """Test MCP microservice with simple questions"""
    print("\n🤖 TESTING MCP MICROSERVICE")
    print("=" * 50)
    
    base_url = "http://localhost:8081/api/chat"
    
    questions = [
        "My name?",
        "mes factures",
        "mes transactions"
    ]
    
    for question in questions:
        print(f"\n📝 Question: {question}")
        
        payload = {
            "conversationId": "test-123",
            "username": "user1",
            "content": question,
            "backend": "CHAT_MCP"
        }
        
        try:
            response = requests.post(base_url, json=payload)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get('response', 'No response')
                print(f"✅ Answer: {answer[:200]}...")
            else:
                print(f"❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    print("🎯 SIMPLE CHAT-MCP TEST")
    print("=" * 70)
    
    # Test Chat-MCP authentication and endpoints
    token = test_chat_mcp_auth()
    if token:
        test_chat_mcp_endpoints(token)
    
    # Test MCP microservice
    test_mcp_microservice()
    
    print("\n🎉 TEST COMPLETED")

if __name__ == "__main__":
    main()
