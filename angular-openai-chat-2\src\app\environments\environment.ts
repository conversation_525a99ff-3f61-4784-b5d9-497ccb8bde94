// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  demo: "saas", // other possible options are creative and modern
  openaiApiKey: "********************************************************************************************************************************************************************",
  mcpServerUrl: "http://***************:8081", // URL of the MCP server
  useMcpServer: true, // Set to false to use direct OpenAI calls

  // Backend Configuration
  chatMcpUrl: "http://***************:8080", // Chat-MCP backend URL
  winMcpUrl: "http://***************:8082", // Win-MCP backend URL
  defaultBackend: "win-mcp", // Default backend: "chat-mcp" or "win-mcp"

  // Authentication Configuration
  authConfig: {
    chatMcp: {
      type: "SINGLE", // Single authentication for Chat-MCP
      endpoints: {
        login: "/api/auth/login"
      }
    },
    winMcp: {
      type: "DUAL", // Dual authentication for Win-MCP
      endpoints: {
        tenantLogin: "/auth/tenant/login",
        userLogin: "/auth/login"
      },
      tenant: {
        username: "0001",
        password: "123456"
      }
    }
  }
};