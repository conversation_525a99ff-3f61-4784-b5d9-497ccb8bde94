#!/usr/bin/env python3
"""
Test the fixed data endpoints in MCP microservice
"""

import requests
import json
import time

def test_fixed_win_mcp_questions():
    """Test Win-MCP questions with fixed endpoints"""
    print("🔧 TESTING FIXED WIN-MCP DATA ENDPOINTS")
    print("=" * 60)
    
    questions = [
        ("quel est mon groupe sanguin?", "Should get blood type from medical profile"),
        ("mes factures?", "Should get invoice data"),
        ("quelles sont mes informations médicales?", "Should get medical profile"),
        ("qui est mon médecin?", "Should get doctor info"),
        ("quelles sont mes allergies?", "Should get allergy info")
    ]
    
    for i, (question, expected) in enumerate(questions, 1):
        print(f"\n🧪 Test {i}: {question}")
        print(f"   Expected: {expected}")
        
        conversation_id = f"test-fixed-{i}-{int(time.time())}"
        username = "user1"
        
        try:
            # Step 1: Send the question
            response1 = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": conversation_id,
                    "content": question,
                    "username": username
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Step 1 Status: {response1.status_code}")
            if response1.status_code == 200:
                data1 = response1.json()
                content1 = data1.get('content', '')
                print(f"   Step 1 Response: {content1[:100]}...")
                
                # Step 2: Select database option if prompted
                if "Souhaitez-vous que je vous réponde" in content1:
                    print(f"   🔄 Step 2: Selecting database option...")
                    
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": conversation_id,  # SAME ID
                            "content": "1",
                            "username": username
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    print(f"   Step 2 Status: {response2.status_code}")
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   Step 2 Response: {content2[:200]}...")
                        
                        # Analyze the response
                        if "je n'ai pas trouvé" in content2.lower() or "information n'est pas disponible" in content2.lower():
                            print(f"   ❌ STILL FAILED: Getting 'info not found'")
                        elif any(keyword in content2.lower() for keyword in ['o+', 'a-', 'dr.', 'hassan', 'alami', 'pénicilline', 'aspirine']):
                            print(f"   🎉 SUCCESS: Got real data!")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                    else:
                        print(f"   ❌ Step 2 failed: {response2.text}")
                else:
                    print(f"   ⚠️  No source selection prompt")
                    
            else:
                print(f"   ❌ Step 1 failed: {response1.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_chat_mcp_questions():
    """Test Chat-MCP questions"""
    print(f"\n💰 TESTING CHAT-MCP QUESTIONS")
    print("=" * 50)
    
    questions = [
        ("my email?", "Should get email from user data"),
        ("my name?", "Should get name from user data"),
        ("my transactions?", "Should get transaction data"),
        ("my invoices?", "Should get invoice data")
    ]
    
    for i, (question, expected) in enumerate(questions, 1):
        print(f"\n🧪 Test {i}: {question}")
        print(f"   Expected: {expected}")
        
        conversation_id = f"test-chat-{i}-{int(time.time())}"
        username = "user1"
        
        try:
            # Step 1: Send the question
            response1 = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": conversation_id,
                    "content": question,
                    "username": username
                },
                headers={'Content-Type': 'application/json'}
            )
            
            if response1.status_code == 200:
                data1 = response1.json()
                content1 = data1.get('content', '')
                
                # Step 2: Select database option if prompted
                if "Souhaitez-vous que je vous réponde" in content1:
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": conversation_id,  # SAME ID
                            "content": "1",
                            "username": username
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   Response: {content2[:200]}...")
                        
                        # Check for Chat-MCP data
                        if "je n'ai pas trouvé" in content2.lower():
                            print(f"   ❌ FAILED: Getting 'info not found'")
                        elif any(keyword in content2.lower() for keyword in ['<EMAIL>', 'test user1', 'transaction', 'facture']):
                            print(f"   🎉 SUCCESS: Got Chat-MCP data!")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                            
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    print("🔧 TESTING FIXED DATA ENDPOINTS")
    print("=" * 70)
    print("Testing the fixes applied to Win-MCP data endpoints...")
    
    # Test Win-MCP with fixed endpoints
    test_fixed_win_mcp_questions()
    
    # Test Chat-MCP for comparison
    test_chat_mcp_questions()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 FIXED ENDPOINTS TEST SUMMARY")
    print(f"=" * 70)
    print(f"✅ Applied fixes:")
    print(f"   - Enabled user-data endpoint: /api/winplus/user-data/{{username}}")
    print(f"   - Enabled sales statistics: /api/winplus/ventes/statistics")
    print(f"   - Enabled products data: /api/winplus/produits")
    print(f"   - Added better error handling and logging")
    print(f"\n💡 EXPECTED RESULTS:")
    print(f"   - Medical questions should now return real data")
    print(f"   - Invoice/sales questions should work")
    print(f"   - Better debugging logs in MCP microservice")
    print(f"\n🔍 CHECK MCP MICROSERVICE LOGS:")
    print(f"   Look for new debug messages:")
    print(f"   - '🔍 SMART WIN-MCP: Fetching data from all available endpoints...'")
    print(f"   - '✅ User data fetched successfully'")
    print(f"   - '✅ Medical profile fetched successfully'")
    print(f"   - '✅ Products data fetched successfully'")

if __name__ == "__main__":
    main()
