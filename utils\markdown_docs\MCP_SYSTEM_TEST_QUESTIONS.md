# 🧪 MCP System - Complete Test Questions Guide

This document contains all possible questions you can test with both **Chat-MCP** and **Win-MCP** backends.

## 🎯 How to Test

1. **Open Angular Frontend**: http://localhost:4200
2. **Select Backend**: Choose either Chat-MCP or Win-MCP
3. **Login**: Use `user1/password` for both backends
4. **Ask Questions**: Copy any question from the lists below
5. **Select Option**: When prompted, choose "1" for database responses

---

## 💰 Chat-MCP Backend Questions

### 👤 **User Profile & Personal Information**
```
What is my name?
Quel est mon nom?
What is my email?
Quelle est mon adresse email?
What is my full name?
Quelles sont mes informations personnelles?
Who am I?
Qui suis-je?
My personal details?
Mes détails personnels?
What is my username?
Quel est mon nom d'utilisateur?
```

### 💳 **Transaction Questions**
```
What are my transactions?
Quelles sont mes transactions?
Show me my recent transactions
Montrez-moi mes transactions récentes
My transaction history?
Mon historique de transactions?
How many transactions do I have?
Combien de transactions ai-je?
What is my total transaction amount?
Quel est le montant total de mes transactions?
My completed transactions?
Mes transactions terminées?
Show me transaction details
Montrez-moi les détails des transactions
My payment history?
Mon historique de paiements?
```

### 🧾 **Invoice Questions**
```
What are my invoices?
Quelles sont mes factures?
Show me my bills
Montrez-moi mes factures
My invoice history?
Mon historique de factures?
How much do I owe?
Combien dois-je?
My paid invoices?
Mes factures payées?
My unpaid invoices?
Mes factures impayées?
Total invoice amount?
Montant total des factures?
Invoice details?
Détails des factures?
```

### 📦 **Order Questions**
```
What are my orders?
Quelles sont mes commandes?
Show me my order history
Montrez-moi mon historique de commandes
My recent orders?
Mes commandes récentes?
Delivered orders?
Commandes livrées?
Pending orders?
Commandes en attente?
Order status?
Statut des commandes?
How many orders do I have?
Combien de commandes ai-je?
Total order amount?
Montant total des commandes?
```

### 💰 **Financial Summary**
```
My financial summary?
Mon résumé financier?
Total spending?
Dépenses totales?
Account balance?
Solde du compte?
Payment summary?
Résumé des paiements?
Financial overview?
Aperçu financier?
```

---

## 🏥 Win-MCP Backend Questions

### 🩺 **Medical Profile Questions**
```
What is my blood type?
Quel est mon groupe sanguin?
What are my allergies?
Quelles sont mes allergies?
Who is my doctor?
Qui est mon médecin?
My medical information?
Mes informations médicales?
Complete medical profile?
Profil médical complet?
My chronic conditions?
Mes conditions chroniques?
Current medications?
Médicaments actuels?
Medical history?
Historique médical?
Emergency contact?
Contact d'urgence?
Insurance information?
Informations d'assurance?
```

### 👨‍⚕️ **Doctor & Healthcare Provider Questions**
```
Doctor contact information?
Informations de contact du médecin?
My physician details?
Détails de mon médecin?
Healthcare provider?
Fournisseur de soins de santé?
Doctor phone number?
Numéro de téléphone du médecin?
Medical practitioner?
Praticien médical?
```

### 💊 **Medication & Allergy Questions**
```
What medications am I taking?
Quels médicaments prends-je?
Drug allergies?
Allergies médicamenteuses?
Medication list?
Liste des médicaments?
Current prescriptions?
Prescriptions actuelles?
Allergy information?
Informations sur les allergies?
Food allergies?
Allergies alimentaires?
```

### 🏥 **Health Metrics Questions**
```
My height and weight?
Ma taille et mon poids?
Health measurements?
Mesures de santé?
BMI information?
Informations sur l'IMC?
Physical characteristics?
Caractéristiques physiques?
Health stats?
Statistiques de santé?
```

### 📋 **Medical Records Questions**
```
Last checkup date?
Date du dernier contrôle?
Medical notes?
Notes médicales?
Health records?
Dossiers de santé?
Medical appointments?
Rendez-vous médicaux?
Health checkup history?
Historique des contrôles de santé?
```

### 💊 **Pharmacy & Products Questions**
```
What products are available?
Quels produits sont disponibles?
Show me the medication catalog
Montrez-moi le catalogue des médicaments
Available medicines?
Médicaments disponibles?
Product inventory?
Inventaire des produits?
Pharmacy stock?
Stock de la pharmacie?
Medicine prices?
Prix des médicaments?
Drug catalog?
Catalogue des médicaments?
Available treatments?
Traitements disponibles?
```

### 📊 **Business & Analytics Questions (Win-MCP)**
```
Dashboard summary?
Résumé du tableau de bord?
Business statistics?
Statistiques commerciales?
Sales data?
Données de vente?
Client information?
Informations sur les clients?
Pharmacy analytics?
Analyses de la pharmacie?
```

---

## 🔄 **Cross-Backend Questions (Test Routing)**

### Questions that should go to **Win-MCP**:
```
Medical questions (blood type, allergies, doctor)
Health-related queries
Medication questions
Pharmacy product inquiries
```

### Questions that should go to **Chat-MCP**:
```
Personal information (name, email)
Financial questions (transactions, invoices)
Order history
Payment information
```

---

## 🧪 **Advanced Test Scenarios**

### **Multi-Step Conversations**
1. Ask a question
2. When prompted, select "1" for database
3. Ask follow-up questions in the same conversation
4. Test conversation memory

### **Backend Switching**
1. Test with Chat-MCP backend
2. Logout and switch to Win-MCP
3. Test with Win-MCP backend
4. Compare responses

### **Error Handling**
```
Invalid questions?
Questions non valides?
Unknown information?
Informations inconnues?
Data not found?
Données non trouvées?
```

### **Language Testing**
- Test questions in both **French** and **English**
- Mix languages in the same conversation
- Test special characters and accents

---

## 📝 **Expected Response Patterns**

### **Chat-MCP Responses Should Include:**
- User email: `<EMAIL>`
- User name: `Test User1`
- Transaction amounts in EUR
- Invoice details with payment status
- Order information with delivery status

### **Win-MCP Responses Should Include:**
- Blood type: `O+`
- Doctor: `Dr. Hassan Alami`
- Allergies: `Pénicilline, Aspirine, Fruits de mer`
- Product catalog with prices in DH
- Medical profile details

---

## 🎯 **Success Criteria**

✅ **Question is answered with real data**  
✅ **Correct backend is used**  
✅ **Response is in French**  
✅ **Data is specific and detailed**  
✅ **No "information not found" errors**

❌ **"Je n'ai pas trouvé d'informations" = Issue**  
❌ **Wrong backend used = Routing problem**  
❌ **Generic/vague response = Data retrieval issue**

---

## 🚀 **Quick Test Commands**

Copy and paste these for rapid testing:

**Chat-MCP Quick Tests:**
```
my email?
my transactions?
my invoices?
```

**Win-MCP Quick Tests:**
```
quel est mon groupe sanguin?
qui est mon médecin?
quelles sont mes allergies?
```

**Happy Testing! 🎉**

---

## 🔍 **Additional Specific Test Questions**

### **Chat-MCP - Detailed Financial Questions**
```
What is my account balance?
Quel est le solde de mon compte?
Show me transaction #13
Montrez-moi la transaction #13
My highest transaction amount?
Mon montant de transaction le plus élevé?
Transactions from last month?
Transactions du mois dernier?
Payment methods used?
Méthodes de paiement utilisées?
Failed transactions?
Transactions échouées?
Refund history?
Historique des remboursements?
Currency used in transactions?
Devise utilisée dans les transactions?
Transaction fees?
Frais de transaction?
```

### **Win-MCP - Detailed Medical Questions**
```
My insurance number?
Mon numéro d'assurance?
Emergency contact details?
Détails du contact d'urgence?
Last medical checkup?
Dernier contrôle médical?
Medical notes about my condition?
Notes médicales sur mon état?
Height and weight information?
Informations sur la taille et le poids?
Birth date from medical records?
Date de naissance des dossiers médicaux?
Chronic medical conditions?
Conditions médicales chroniques?
Current medication dosage?
Dosage des médicaments actuels?
Medical appointment history?
Historique des rendez-vous médicaux?
```

### **Win-MCP - Pharmacy Business Questions**
```
How many products are in stock?
Combien de produits sont en stock?
Most expensive medication?
Médicament le plus cher?
Cheapest product available?
Produit le moins cher disponible?
Products requiring prescription?
Produits nécessitant une ordonnance?
Generic medications available?
Médicaments génériques disponibles?
Product categories?
Catégories de produits?
Supplier information?
Informations sur les fournisseurs?
Sales statistics?
Statistiques de vente?
Client database information?
Informations de la base de données clients?
```

### **Complex Multi-Part Questions**
```
What is my complete profile including medical and personal information?
Quel est mon profil complet incluant les informations médicales et personnelles?
Show me everything you know about me
Montrez-moi tout ce que vous savez sur moi
Complete summary of my data
Résumé complet de mes données
All my information from the database
Toutes mes informations de la base de données
```

### **Comparative Questions**
```
Compare my transactions from different months
Comparez mes transactions de différents mois
Show differences between paid and unpaid invoices
Montrez les différences entre les factures payées et impayées
Compare product prices
Comparez les prix des produits
Medical information vs personal information
Informations médicales vs informations personnelles
```

### **Statistical Questions**
```
Average transaction amount?
Montant moyen des transactions?
Total number of medical records?
Nombre total de dossiers médicaux?
Percentage of completed orders?
Pourcentage de commandes terminées?
Most frequent medication type?
Type de médicament le plus fréquent?
```

---

## 🎭 **Role-Playing Test Scenarios**

### **Patient Scenario (Win-MCP)**
```
I'm feeling unwell, what medications do I usually take?
Je ne me sens pas bien, quels médicaments prends-je habituellement?
I need to contact my doctor urgently
J'ai besoin de contacter mon médecin de toute urgence
What should I avoid due to my allergies?
Que dois-je éviter à cause de mes allergies?
```

### **Customer Scenario (Chat-MCP)**
```
I want to check my recent purchases
Je veux vérifier mes achats récents
Is there any pending payment?
Y a-t-il un paiement en attente?
What's my spending pattern?
Quel est mon modèle de dépenses?
```

### **Emergency Scenario (Win-MCP)**
```
Emergency contact information needed
Informations de contact d'urgence nécessaires
Critical medical information
Informations médicales critiques
Allergy alert information
Informations d'alerte d'allergie
```

---

## 🌐 **Multi-Language Testing**

### **Mixed Language Questions**
```
What is mon groupe sanguin?
Quelles sont my allergies?
Show me mes transactions?
Quel est my email address?
```

### **Special Characters & Accents**
```
Médecin spécialisé?
Numéro de téléphone?
Adresse électronique?
Médicaments spéciaux?
Données financières?
```

---

## 🔧 **Technical Testing Questions**

### **Data Validation**
```
Show me data format for transactions
Montrez-moi le format des données pour les transactions
What fields are available in my profile?
Quels champs sont disponibles dans mon profil?
Database schema information?
Informations sur le schéma de base de données?
```

### **Error Boundary Testing**
```
Non-existent user information?
Informations utilisateur inexistantes?
Invalid medical data?
Données médicales invalides?
Corrupted transaction records?
Enregistrements de transaction corrompus?
```

---

## 📊 **Performance Testing Questions**

### **Large Data Queries**
```
All my historical data
Toutes mes données historiques
Complete transaction log
Journal complet des transactions
Full medical history
Historique médical complet
Everything in the database about me
Tout dans la base de données sur moi
```

### **Rapid Fire Questions**
Test by asking multiple questions quickly in succession:
```
1. my email?
2. my name?
3. my transactions?
4. my invoices?
5. my orders?
```

---

## 🎯 **Final Validation Checklist**

For each question tested, verify:

- [ ] **Correct Backend Used**: Medical → Win-MCP, Financial → Chat-MCP
- [ ] **Real Data Returned**: Specific values, not generic responses
- [ ] **French Response**: All responses should be in French
- [ ] **Complete Information**: Detailed, not just basic answers
- [ ] **No Errors**: No "information not found" messages
- [ ] **Conversation Flow**: Multi-step conversations work properly
- [ ] **Backend Switching**: Can switch between backends successfully

**🎉 Comprehensive Testing Complete!**
