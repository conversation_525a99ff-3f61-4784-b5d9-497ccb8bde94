#!/usr/bin/env python3
"""
Direct API test for knowledge toggle functionality
"""

import requests
import json

def test_api_directly():
    """Test the API directly with different knowledge modes"""
    
    base_url = "http://localhost:8081"
    
    # Test internal mode
    print("🔍 Testing INTERNAL mode:")
    internal_payload = {
        "conversationId": "test-internal-123",
        "content": "mes factures",
        "username": "user1",
        "backend": "win-mcp",
        "knowledgeMode": "internal"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/chat",
            json=internal_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data.get('content', 'No content')[:200]}...")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Exception: {str(e)}")
    
    print("\n" + "="*50 + "\n")
    
    # Test general mode
    print("🌍 Testing GENERAL mode:")
    general_payload = {
        "conversationId": "test-general-123",
        "content": "capitale du maroc",
        "username": "user1",
        "backend": "win-mcp",
        "knowledgeMode": "general"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/chat",
            json=general_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {data.get('content', 'No content')[:200]}...")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Exception: {str(e)}")

if __name__ == "__main__":
    test_api_directly()
