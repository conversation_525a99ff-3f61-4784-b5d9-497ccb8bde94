#!/usr/bin/env python3
"""
Final test for MCP microservice with fournisseur integration
"""

import requests
import json
import time

def test_mcp_fournisseur():
    """Test the fournisseur integration with MCP Smart AI"""
    
    print("🎯 TESTING FOURNISSEUR INTEGRATION WITH MCP SMART AI")
    print("=" * 60)
    
    # Test the new MCP endpoint
    url = "http://localhost:8081/api/mcp/smart-winmcp"
    payload = {
        "username": "testuser",
        "question": "my suppliers?"
    }
    
    print(f"🔍 Testing: {payload['question']}")
    print(f"📞 Calling: {url}")
    
    try:
        response = requests.post(url, json=payload, timeout=60)
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            response_text = response.text
            print(f"✅ SUCCESS! Response received:")
            print("=" * 40)
            print(response_text)
            print("=" * 40)
            
            # Check if response contains supplier-related information
            supplier_keywords = [
                "fournisseur", "supplier", "laboratoire", "laboratory",
                "FOUR001", "FOUR002", "Laboratoires Pharmaceutiques",
                "Sanofi", "<EMAIL>", "Casablanca"
            ]
            
            found_keywords = [kw for kw in supplier_keywords if kw.lower() in response_text.lower()]
            
            if found_keywords:
                print(f"\n🎉 EXCELLENT! Found supplier data!")
                print(f"🔍 Keywords found: {', '.join(found_keywords)}")
                print(f"\n✅ FOURNISSEUR DATA TYPE SUCCESSFULLY INTEGRATED!")
                return True
            else:
                print(f"\n⚠️  Response doesn't contain expected supplier data")
                return False
        else:
            print(f"❌ FAILED: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        return False

def test_multiple_supplier_questions():
    """Test various supplier questions"""
    
    print(f"\n🔍 TESTING MULTIPLE SUPPLIER QUESTIONS")
    print("=" * 50)
    
    questions = [
        "mes fournisseurs",
        "supplier information", 
        "laboratoires",
        "fournisseurs actifs",
        "supplier statistics"
    ]
    
    successful = 0
    
    for i, question in enumerate(questions, 1):
        print(f"\n📋 Test {i}/{len(questions)}: '{question}'")
        
        payload = {
            "username": "testuser",
            "question": question
        }
        
        try:
            response = requests.post(
                "http://localhost:8081/api/mcp/smart-winmcp",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS")
                successful += 1
            else:
                print(f"   ❌ FAILED: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
        
        time.sleep(1)  # Small delay between requests
    
    print(f"\n📊 Results: {successful}/{len(questions)} successful")
    return successful > 0

if __name__ == "__main__":
    print("🚀 STARTING FINAL FOURNISSEUR INTEGRATION TEST")
    print("=" * 60)
    
    # Test basic supplier question
    basic_success = test_mcp_fournisseur()
    
    # Test multiple questions
    multiple_success = test_multiple_supplier_questions()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS:")
    print("=" * 60)
    
    if basic_success:
        print("✅ Basic supplier integration: SUCCESS")
    else:
        print("❌ Basic supplier integration: FAILED")
    
    if multiple_success:
        print("✅ Multiple supplier questions: SUCCESS")
    else:
        print("❌ Multiple supplier questions: FAILED")
    
    if basic_success and multiple_success:
        print(f"\n🎉 CONGRATULATIONS!")
        print("🏆 FOURNISSEUR DATA TYPE SUCCESSFULLY ADDED TO WIN-MCP!")
        print("✅ Backend: Win-MCP provides supplier data")
        print("✅ Microservice: MCP processes supplier questions")
        print("✅ AI: Responds with supplier information")
        print("✅ Integration: End-to-end functionality working!")
    else:
        print(f"\n⚠️  Some tests failed. Check the logs for details.")
    
    print(f"\n📝 Summary:")
    print("   - New Fournisseur entity, repository, service, controller created")
    print("   - Sample supplier data generated")
    print("   - MCP microservice updated with supplier integration")
    print("   - Smart AI can now answer supplier-related questions")
    print("   - Both French and English questions supported")
