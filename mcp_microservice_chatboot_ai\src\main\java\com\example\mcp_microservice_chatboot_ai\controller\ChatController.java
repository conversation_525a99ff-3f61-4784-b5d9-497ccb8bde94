package com.example.mcp_microservice_chatboot_ai.controller;

import com.example.mcp_microservice_chatboot_ai.model.dto.ChatRequest;
import com.example.mcp_microservice_chatboot_ai.model.dto.ChatResponse;
import com.example.mcp_microservice_chatboot_ai.model.dto.McpChatResponseDto;
import com.example.mcp_microservice_chatboot_ai.service.AiChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * Controller for handling chat-related endpoints.
 */

/**
 * Role: REST controller for chat-related endpoints
    Purpose:
    Exposes endpoint for sending chat messages
    Delegates message processing to AiChatService
    Returns AI-generated responses to clients
 */
@RestController
@RequestMapping("/api/chat")
public class ChatController {

    private final AiChatService aiChatService;

    @Autowired
    public ChatController(AiChatService aiChatService) {
        this.aiChatService = aiChatService;
    }

    /**
     * Endpoint for sending a chat message and getting a response.
     * Returns response in format compatible with Angular frontend.
     *
     * @param chatRequest The chat request
     * @return A Mono containing the chat response in Angular-compatible format
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<McpChatResponseDto> sendMessage(@RequestBody ChatRequest chatRequest) {
        System.out.println("🔄 CHAT CONTROLLER: Received message: " + chatRequest.getContent());
        System.out.println("🧠 CHAT CONTROLLER: Knowledge mode: " + chatRequest.getKnowledgeMode());

        return aiChatService.processChat(chatRequest)
                .map(response -> McpChatResponseDto.builder()
                        .id(response.getId())
                        .conversationId(response.getConversationId())
                        .role(response.getRole().toString())
                        .content(response.getContent())
                        .timestamp(response.getTimestamp().toString())
                        .build())
                .doOnSuccess(result -> System.out.println("✅ CHAT CONTROLLER: Response generated successfully"))
                .doOnError(error -> System.out.println("❌ CHAT CONTROLLER: Error - " + error.getMessage()));
    }
}
