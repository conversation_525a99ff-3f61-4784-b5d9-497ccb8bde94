# 🔄 MCP Reactive Programming & Integration Analysis

## 📋 **Question 1: Why Reactive Programming in MCP App?**

### **🎯 Purpose of Reactive Programming in MCP:**

#### **1. 🚀 Non-Blocking I/O Operations**
```java
// Without Reactive (Blocking)
String userData = winMcpWebClient.get().uri("/api/winplus/user-data").retrieve().block(); // BLOCKS THREAD!
String salesData = winMcpWebClient.get().uri("/api/winplus/ventes").retrieve().block();   // BLOCKS THREAD!

// With Reactive (Non-Blocking)
Mono<String> userDataMono = winMcpWebClient.get().uri("/api/winplus/user-data").retrieve().bodyToMono(String.class);
Mono<String> salesDataMono = winMcpWebClient.get().uri("/api/winplus/ventes").retrieve().bodyToMono(String.class);
```

#### **2. 🔄 Parallel Data Fetching**
```java
// Smart MCP fetches MULTIPLE APIs simultaneously
Mono.zip(userDataMono, clientDataMono, salesDataMono, productsDataMono, dashboardDataMono)
    .map(tuple -> {
        // Process all data together - MUCH FASTER!
        return formatSmartWinMcpData(tuple.getT1(), tuple.getT2(), tuple.getT3(), tuple.getT4(), tuple.getT5());
    });
```

#### **3. ⚡ Performance Benefits in MCP:**
- **Multiple API calls:** MCP fetches 5-6 different data sources per request
- **OpenAI API calls:** Can take 500-2000ms each
- **Database queries:** Multiple backend database operations
- **User scalability:** Handle multiple users simultaneously without blocking

#### **4. 🎯 Specific MCP Use Cases:**
```java
// Example: Smart Win-MCP Data Fetching
return Mono.zip(
    getUserData(),      // ~200ms
    getClientData(),    // ~150ms
    getSalesData(),     // ~300ms
    getProductsData(),  // ~250ms
    getDashboardData()  // ~100ms
)
// Total: ~1000ms parallel vs ~1000ms sequential = 5x faster!
```

---

## 📋 **Question 2: Mono vs Flux in AiChatService.java**

### **🔍 Current Usage Analysis:**

#### **✅ Where Mono is Used (Single Response):**

1. **`processChat()` → Mono<ChatResponse>**
```java
public Mono<ChatResponse> processChat(ChatRequest chatRequest)
// Purpose: Single chat response per request
```

2. **`generateDatabaseResponse()` → Mono<ChatResponse>**
```java
private Mono<ChatResponse> generateDatabaseResponse(...)
// Purpose: Single response after database query
```

3. **`generateGreetingResponse()` → Mono<ChatResponse>**
```java
private Mono<ChatResponse> generateGreetingResponse(...)
// Purpose: Single greeting response
```

4. **`getChatMcpDataReactive()` → Mono<String>**
```java
private Mono<String> getChatMcpDataReactive(...)
// Purpose: Single formatted data string from Chat-MCP
```

5. **`getWinMcpDataReactive()` → Mono<String>**
```java
private Mono<String> getWinMcpDataReactive(...)
// Purpose: Single formatted data string from Win-MCP
```

#### **❌ Where Flux is NOT Used (But Could Be):**

**Flux is for streams of multiple items. In MCP, we don't currently need it because:**
- **Single chat response** per request (not streaming)
- **Single user** per conversation
- **Single formatted result** from data aggregation

#### **🚀 Potential Flux Use Cases (Future):**
```java
// Streaming chat responses (like ChatGPT)
public Flux<ChatResponseChunk> streamChatResponse(ChatRequest request)

// Multiple conversation history
public Flux<ChatMessage> getConversationHistory(String conversationId)

// Real-time data updates
public Flux<DataUpdate> subscribeToDataUpdates(String username)
```

---

## 📋 **Question 3: Currently Used Functions Analysis**

### **🔧 WinMcpToolsFunctions.java - Active Functions:**

#### **✅ CURRENTLY USED:**
1. **`getSmartWinMcpDataTool()`** - **PRIMARY FUNCTION**
```java
// Lines 45-158: Main Smart MCP function
// Fetches ALL data types simultaneously
// Used by: AiChatService.getWinMcpDataReactive()
```

#### **❌ LEGACY FUNCTIONS (Not Used in Smart Mode):**
```java
getWinPlusUserProfileTool()    // Lines 164-197: Individual user profile
getWinPlusClientInfoTool()     // Lines 202-225: Individual client info
getWinPlusSalesTool()          // Lines 230-267: Individual sales data
getWinPlusProductsTool()       // Lines 272-295: Individual products
getWinPlusPurchasesTool()      // Lines 300-323: Individual purchases
getWinPlusDashboardTool()      // Lines 328-351: Individual dashboard
```

### **🔧 RealDataSimulationWinplus.java - Active Endpoints:**

#### **✅ CURRENTLY USED by Smart MCP:**
```java
@GetMapping("/api/winplus/user-data/{username}")           // Line 366: User profile data
@GetMapping("/api/winplus/clients/code/{codeClient}")      // Line 76: Client by code
@GetMapping("/api/winplus/ventes/statistics")              // Line 227: Sales statistics
@GetMapping("/api/winplus/produits")                       // Line 104: Products list
@GetMapping("/api/winplus/dashboard/summary")              // Line 406: Dashboard summary
@GetMapping("/api/winplus/ventes/client/{clientId}")       // Line 201: Client sales
```

#### **❌ AVAILABLE BUT UNUSED:**
```java
@GetMapping("/api/winplus/clients")                        // Line 46: All clients
@GetMapping("/api/winplus/ventes")                         // Line 168: All sales
@GetMapping("/api/winplus/achats")                         // Line 256: All purchases
// + 15 other specialized endpoints
```

---

## 📋 **Question 4: Adding New Personal Data - Step-by-Step Guide**

### **🎯 Example: Adding "Personal Profile Details"**

#### **Step 1: Backend (Win-MCP) - Add New Endpoint**

**A. Create New Entity (if needed):**
```java
// win-mcp/src/main/java/com/chatbootmcp/chatmcp/entity/PersonalProfile.java
@Entity
public class PersonalProfile {
    @Id
    private Long id;
    private String username;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String address;
    private LocalDate birthDate;
    private String profession;
    private String emergencyContact;
    // getters/setters
}
```

**B. Create Repository:**
```java
// win-mcp/src/main/java/com/chatbootmcp/chatmcp/repository/PersonalProfileRepository.java
public interface PersonalProfileRepository extends JpaRepository<PersonalProfile, Long> {
    Optional<PersonalProfile> findByUsername(String username);
}
```

**C. Add Controller Endpoint:**
```java
// In RealDataSimulationWinplus.java - Add this method:
@GetMapping("/api/winplus/personal-profile/{username}")
public ResponseEntity<Map<String, Object>> getPersonalProfile(@PathVariable String username) {
    Optional<PersonalProfile> profileOpt = personalProfileRepository.findByUsername(username);

    if (!profileOpt.isPresent()) {
        return ResponseEntity.notFound().build();
    }

    Map<String, Object> response = new HashMap<>();
    response.put("personalProfile", profileOpt.get());
    return ResponseEntity.ok(response);
}
```

**D. Add Sample Data:**
```java
// In DataInitializer.java
PersonalProfile profile1 = new PersonalProfile();
profile1.setUsername("user1");
profile1.setFirstName("Ahmed");
profile1.setLastName("Benali");
profile1.setPhoneNumber("+212 6 12 34 56 78");
profile1.setAddress("123 Rue Mohammed V, Casablanca");
profile1.setBirthDate(LocalDate.of(1985, 5, 15));
profile1.setProfession("Pharmacien");
profile1.setEmergencyContact("Fatima Benali - +212 6 87 65 43 21");
personalProfileRepository.save(profile1);
```

#### **Step 2: MCP Microservice - Update Smart Tool**

**A. Add New Data Fetching in WinMcpToolsFunctions.java:**
```java
// In getSmartWinMcpDataTool() method, add new Mono:
Mono<Map> personalProfileMono = webClient.get()
    .uri("/api/winplus/personal-profile/" + request.username())
    .header("Authorization", "Bearer " + token)
    .retrieve()
    .bodyToMono(Map.class)
    .onErrorReturn(new HashMap<>());
```

**B. Update Mono.zip Combination:**
```java
// Update the Mono.zip to include new data:
return Mono.zip(userDataMono, clientDataMono, salesDataMono, productsDataMono,
                dashboardDataMono, clientSalesDataMono, personalProfileMono)
    .map(tuple -> {
        Map<String, Object> userData = (Map<String, Object>) tuple.getT1();
        Map<String, Object> clientData = (Map<String, Object>) tuple.getT2();
        Map<String, Object> salesStats = (Map<String, Object>) tuple.getT3();
        Map<String, Object> productsData = (Map<String, Object>) tuple.getT4();
        Map<String, Object> dashboardData = (Map<String, Object>) tuple.getT5();
        Map<String, Object> clientSalesData = (Map<String, Object>) tuple.getT6();
        Map<String, Object> personalProfileData = (Map<String, Object>) tuple.getT7(); // NEW!

        return formatSmartWinMcpData(userData, clientData, salesStats, productsData,
                                   dashboardData, clientSalesData, personalProfileData);
    });
```

**C. Update Formatting Method:**
```java
// Update formatSmartWinMcpData method signature:
private String formatSmartWinMcpData(Map<String, Object> userData, Map<String, Object> clientData,
                                   Map<String, Object> salesStats, Map<String, Object> productsData,
                                   Map<String, Object> dashboardData, Map<String, Object> clientSalesData,
                                   Map<String, Object> personalProfileData) { // NEW PARAMETER!

    StringBuilder sb = new StringBuilder();
    sb.append("=== 🧠 SMART WIN-MCP DATA ANALYSIS ===\n\n");

    // Add new personal profile section:
    if (personalProfileData != null && personalProfileData.containsKey("personalProfile")) {
        Map<String, Object> profile = (Map<String, Object>) personalProfileData.get("personalProfile");
        sb.append("👤 PROFIL PERSONNEL DÉTAILLÉ:\n");
        sb.append("  • Prénom: ").append(profile.get("firstName")).append("\n");
        sb.append("  • Nom: ").append(profile.get("lastName")).append("\n");
        sb.append("  • Téléphone: ").append(profile.get("phoneNumber")).append("\n");
        sb.append("  • Adresse: ").append(profile.get("address")).append("\n");
        sb.append("  • Date de naissance: ").append(profile.get("birthDate")).append("\n");
        sb.append("  • Profession: ").append(profile.get("profession")).append("\n");
        sb.append("  • Contact d'urgence: ").append(profile.get("emergencyContact")).append("\n\n");
    }

    // ... rest of existing formatting
}
```

#### **Step 3: Test New Personal Data**

**A. Test Questions:**
```
"quelles sont mes informations personnelles complètes?"
"quel est mon nom complet?"
"quelle est mon adresse?"
"quel est mon numéro de téléphone?"
"qui est mon contact d'urgence?"
"quelle est ma profession?"
```

**B. Expected Response:**
```
👤 PROFIL PERSONNEL DÉTAILLÉ:
  • Prénom: Ahmed
  • Nom: Benali
  • Téléphone: +212 6 12 34 56 78
  • Adresse: 123 Rue Mohammed V, Casablanca
  • Date de naissance: 1985-05-15
  • Profession: Pharmacien
  • Contact d'urgence: Fatima Benali - +212 6 87 65 43 21
```

---

## 🎯 **Key Principles for Adding New Data:**

### **1. 🔄 Reactive Chain Pattern:**
```java
// Always follow this pattern:
New API Endpoint → New Mono → Update Zip → Update Formatting → Test
```

### **2. 🧠 AI-Powered Understanding:**
- **No hardcoded keywords** needed
- **AI automatically understands** new data types
- **Smart formatting** makes data accessible to AI

### **3. ⚡ Performance Optimization:**
- **Parallel fetching** of all data sources
- **Single AI call** with comprehensive context
- **Non-blocking** reactive streams

### **4. 🔧 Error Handling:**
```java
.onErrorReturn(new HashMap<>())  // Graceful fallback for each data source
```

This approach makes the MCP system **infinitely extensible** - just add data sources and the AI automatically handles new question types! 🚀

---

## 🔬 **Practical Code Examples**

### **🔄 Reactive vs Non-Reactive Performance Comparison:**

#### **❌ Blocking Approach (Slow):**
```java
// Sequential blocking calls - TOTAL: ~1500ms
String userData = webClient.get().uri("/api/user-data").retrieve().block();        // 300ms
String salesData = webClient.get().uri("/api/sales").retrieve().block();          // 400ms
String productData = webClient.get().uri("/api/products").retrieve().block();     // 350ms
String dashboardData = webClient.get().uri("/api/dashboard").retrieve().block();  // 250ms
String clientData = webClient.get().uri("/api/clients").retrieve().block();       // 200ms
// Process data...
```

#### **✅ Reactive Approach (Fast):**
```java
// Parallel non-blocking calls - TOTAL: ~400ms (fastest API call)
Mono<String> userMono = webClient.get().uri("/api/user-data").retrieve().bodyToMono(String.class);
Mono<String> salesMono = webClient.get().uri("/api/sales").retrieve().bodyToMono(String.class);
Mono<String> productMono = webClient.get().uri("/api/products").retrieve().bodyToMono(String.class);
Mono<String> dashboardMono = webClient.get().uri("/api/dashboard").retrieve().bodyToMono(String.class);
Mono<String> clientMono = webClient.get().uri("/api/clients").retrieve().bodyToMono(String.class);

return Mono.zip(userMono, salesMono, productMono, dashboardMono, clientMono)
    .map(tuple -> processAllData(tuple.getT1(), tuple.getT2(), tuple.getT3(), tuple.getT4(), tuple.getT5()));
```

### **🎯 Real MCP Performance Impact:**

#### **Current Smart MCP Data Fetching:**
```java
// 6 parallel API calls in WinMcpToolsFunctions.java:
Mono.zip(
    userDataMono,      // /api/winplus/user-data/{username}
    clientDataMono,    // /api/winplus/clients/code/{username}
    salesDataMono,     // /api/winplus/ventes/statistics
    productsDataMono,  // /api/winplus/produits
    dashboardDataMono, // /api/winplus/dashboard/summary
    clientSalesDataMono // /api/winplus/ventes/client/{clientId}
)
// Result: 6 API calls in parallel instead of sequential = 6x faster!
```

### **🔧 Complete Working Example - Adding Medical History:**

#### **Step 1: Backend Entity**
```java
@Entity
@Table(name = "medical_history")
public class MedicalHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String username;
    private String allergies;
    private String chronicConditions;
    private String currentMedications;
    private String lastCheckupDate;
    private String doctorName;
    private String bloodType;
    private String emergencyMedicalInfo;

    // Constructors, getters, setters...
}
```

#### **Step 2: Repository**
```java
public interface MedicalHistoryRepository extends JpaRepository<MedicalHistory, Long> {
    Optional<MedicalHistory> findByUsername(String username);
    List<MedicalHistory> findByUsernameOrderByLastCheckupDateDesc(String username);
}
```

#### **Step 3: Controller Endpoint**
```java
@GetMapping("/api/winplus/medical-history/{username}")
public ResponseEntity<Map<String, Object>> getMedicalHistory(@PathVariable String username) {
    try {
        Optional<MedicalHistory> historyOpt = medicalHistoryRepository.findByUsername(username);

        Map<String, Object> response = new HashMap<>();
        if (historyOpt.isPresent()) {
            response.put("medicalHistory", historyOpt.get());
            response.put("status", "found");
        } else {
            response.put("medicalHistory", null);
            response.put("status", "not_found");
        }

        return ResponseEntity.ok(response);
    } catch (Exception e) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", "Failed to fetch medical history: " + e.getMessage());
        return ResponseEntity.status(500).body(errorResponse);
    }
}
```

#### **Step 4: Update Smart MCP Tool**
```java
// In WinMcpToolsFunctions.java - getSmartWinMcpDataTool()

// Add new Mono for medical history
Mono<Map> medicalHistoryMono = webClient.get()
    .uri("/api/winplus/medical-history/" + request.username())
    .header("Authorization", "Bearer " + token)
    .retrieve()
    .bodyToMono(Map.class)
    .onErrorReturn(Map.of("error", "Medical history not available"));

// Update Mono.zip (add medicalHistoryMono as 7th parameter)
return Mono.zip(userDataMono, clientDataMono, salesDataMono, productsDataMono,
                dashboardDataMono, clientSalesDataMono, medicalHistoryMono)
    .map(tuple -> {
        // Extract all data including medical history
        Map<String, Object> medicalHistory = (Map<String, Object>) tuple.getT7();

        return formatSmartWinMcpData(userData, clientData, salesStats, productsData,
                                   dashboardData, clientSalesData, medicalHistory);
    });
```

#### **Step 5: Update Formatting**
```java
private String formatSmartWinMcpData(..., Map<String, Object> medicalHistoryData) {
    StringBuilder sb = new StringBuilder();
    sb.append("=== 🧠 SMART WIN-MCP DATA ANALYSIS ===\n\n");

    // Add medical history section
    if (medicalHistoryData != null && medicalHistoryData.containsKey("medicalHistory")) {
        Map<String, Object> history = (Map<String, Object>) medicalHistoryData.get("medicalHistory");
        sb.append("🏥 HISTORIQUE MÉDICAL:\n");
        sb.append("  • Allergies: ").append(history.get("allergies")).append("\n");
        sb.append("  • Conditions chroniques: ").append(history.get("chronicConditions")).append("\n");
        sb.append("  • Médicaments actuels: ").append(history.get("currentMedications")).append("\n");
        sb.append("  • Dernière visite: ").append(history.get("lastCheckupDate")).append("\n");
        sb.append("  • Médecin traitant: ").append(history.get("doctorName")).append("\n");
        sb.append("  • Groupe sanguin: ").append(history.get("bloodType")).append("\n");
        sb.append("  • Info médicale d'urgence: ").append(history.get("emergencyMedicalInfo")).append("\n\n");
    }

    // ... rest of existing formatting
}
```

#### **Step 6: Test Questions**
```
"quelles sont mes allergies?"
"quels médicaments je prends actuellement?"
"quel est mon groupe sanguin?"
"qui est mon médecin traitant?"
"quand était ma dernière visite médicale?"
"mon historique médical complet?"
```

### **🎯 Why This Approach is Revolutionary:**

1. **🧠 Zero Configuration:** AI automatically understands new data types
2. **⚡ Maximum Performance:** All data fetched in parallel
3. **🔄 Infinite Scalability:** Add unlimited data sources
4. **🛡️ Error Resilience:** Each data source fails independently
5. **🎯 Context Awareness:** AI gets complete picture for intelligent responses

### **📊 Performance Metrics:**

| Approach | API Calls | Time | Scalability |
|----------|-----------|------|-------------|
| **Sequential Blocking** | 6 calls × 200ms = 1200ms | ❌ Slow | ❌ Poor |
| **Reactive Parallel** | 6 calls in parallel = 200ms | ✅ Fast | ✅ Excellent |
| **With New Data** | 7+ calls in parallel = 200ms | ✅ Fast | ✅ Excellent |

The reactive approach maintains **constant performance** regardless of the number of data sources! 🚀

---

## 📋 **PRECISE ANSWERS TO YOUR QUESTIONS**

### **🎯 Question 3: Currently Used Functions - EXACT STATUS**

#### **✅ WinMcpToolsFunctions.java - ACTIVE FUNCTIONS:**

**🧠 PRIMARY FUNCTION (Currently Used):**
```java
getSmartWinMcpDataTool()  // Lines 45-158 - MAIN SMART MCP FUNCTION
```
**Used by:** `AiChatService.getWinMcpDataReactive()` → Line 381-387

**❌ LEGACY FUNCTIONS (Available but NOT Used in Smart Mode):**
```java
getWinPlusUserProfileTool()    // Lines 164-197 - Individual user profile
getWinPlusClientInfoTool()     // Lines 202-225 - Individual client info
getWinPlusSalesTool()          // Lines 230-267 - Individual sales data
getWinPlusProductsTool()       // Lines 272-295 - Individual products
getWinPlusPurchasesTool()      // Lines 300-323 - Individual purchases
getWinPlusDashboardTool()      // Lines 328-351 - Individual dashboard
```

#### **✅ RealDataSimulationWinplus.java - ACTIVE ENDPOINTS:**

**🔥 CURRENTLY USED by Smart MCP (6 parallel calls):**
```java
/api/winplus/user-data/{username}           // Line 366 - User profile
/api/winplus/clients/code/{codeClient}      // Line 76  - Client by code
/api/winplus/ventes/statistics              // Line 227 - Sales statistics
/api/winplus/produits                       // Line 104 - Products list
/api/winplus/dashboard/summary              // Line 406 - Dashboard summary
/api/winplus/ventes/client/{clientId}       // Line 201 - Client-specific sales
```

**📊 EXACT REACTIVE CHAIN (Lines 70-121):**
```java
// 6 Parallel Mono calls:
Mono<Map> userDataMono = webClient.get().uri("/api/winplus/user-data/" + username)
Mono<Map> clientDataMono = webClient.get().uri("/api/winplus/clients/code/" + username)
Mono<Map> salesDataMono = webClient.get().uri("/api/winplus/ventes/statistics")
Mono<Map> productsDataMono = webClient.get().uri("/api/winplus/produits?size=50")
Mono<Map> dashboardDataMono = webClient.get().uri("/api/winplus/dashboard/summary")
Mono<Map> clientSalesDataMono = // Dynamic based on client ID

// Combined with Mono.zip:
return Mono.zip(userDataMono, clientDataMono, salesDataMono, productsDataMono, dashboardDataMono, clientSalesDataMono)
```

### **🎯 Question 4: Adding Personal Data - EXACT IMPLEMENTATION**

#### **🔧 STEP-BY-STEP: Adding "Personal Medical Profile"**

**Step 1: Backend Entity (Win-MCP)**
```java
// win-mcp/src/main/java/com/chatbootmcp/chatmcp/entity/PersonalMedicalProfile.java
@Entity
@Table(name = "personal_medical_profiles")
public class PersonalMedicalProfile {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true)
    private String username;

    private String bloodType;
    private String allergies;
    private String chronicConditions;
    private String emergencyContact;
    private String emergencyPhone;
    private String insuranceNumber;
    private String doctorName;
    private String doctorPhone;

    // Constructors, getters, setters...
}
```

**Step 2: Repository**
```java
// win-mcp/src/main/java/com/chatbootmcp/chatmcp/repository/PersonalMedicalProfileRepository.java
public interface PersonalMedicalProfileRepository extends JpaRepository<PersonalMedicalProfile, Long> {
    Optional<PersonalMedicalProfile> findByUsername(String username);
}
```

**Step 3: Controller Endpoint**
```java
// ADD TO: win-mcp/src/main/java/com/chatbootmcp/chatmcp/controller/RealDataSimulationWinplus.java

@Autowired
private PersonalMedicalProfileRepository medicalProfileRepository;

@GetMapping("/api/winplus/medical-profile/{username}")
public ResponseEntity<Map<String, Object>> getMedicalProfile(@PathVariable String username) {
    Optional<PersonalMedicalProfile> profileOpt = medicalProfileRepository.findByUsername(username);

    Map<String, Object> response = new HashMap<>();
    if (profileOpt.isPresent()) {
        response.put("medicalProfile", profileOpt.get());
        response.put("status", "found");
    } else {
        response.put("medicalProfile", null);
        response.put("status", "not_found");
        response.put("message", "No medical profile found for user: " + username);
    }

    return ResponseEntity.ok(response);
}
```

**Step 4: Sample Data (DataInitializer.java)**
```java
// ADD TO: win-mcp/src/main/java/com/chatbootmcp/chatmcp/config/DataInitializer.java

PersonalMedicalProfile medProfile1 = new PersonalMedicalProfile();
medProfile1.setUsername("user1");
medProfile1.setBloodType("O+");
medProfile1.setAllergies("Pénicilline, Aspirine");
medProfile1.setChronicConditions("Hypertension, Diabète Type 2");
medProfile1.setEmergencyContact("Fatima Benali (épouse)");
medProfile1.setEmergencyPhone("+212 6 87 65 43 21");
medProfile1.setInsuranceNumber("CNSS-123456789");
medProfile1.setDoctorName("Dr. Hassan Alami");
medProfile1.setDoctorPhone("+212 5 22 34 56 78");
medicalProfileRepository.save(medProfile1);
```

**Step 5: Update Smart MCP Tool**
```java
// MODIFY: mcp_microservice_chatboot_ai/src/main/java/com/example/mcp_microservice_chatboot_ai/service/WinMcpToolsFunctions.java

// ADD new Mono in getSmartWinMcpDataTool() method (around line 104):
Mono<Map> medicalProfileMono = webClient.get()
    .uri("/api/winplus/medical-profile/" + request.username())
    .header("Authorization", "Bearer " + token)
    .retrieve()
    .bodyToMono(Map.class)
    .onErrorReturn(Map.of("status", "error", "message", "Medical profile not available"));

// UPDATE Mono.zip (line 121) to include 7th parameter:
return Mono.zip(userDataMono, clientDataMono, salesDataMono, productsDataMono,
                dashboardDataMono, clientSalesDataMono, medicalProfileMono)
    .map(tuple -> {
        Map<String, Object> userData = (Map<String, Object>) tuple.getT1();
        Map<String, Object> clientData = (Map<String, Object>) tuple.getT2();
        Map<String, Object> salesStats = (Map<String, Object>) tuple.getT3();
        Map<String, Object> productsData = (Map<String, Object>) tuple.getT4();
        Map<String, Object> dashboardData = (Map<String, Object>) tuple.getT5();
        Map<String, Object> clientSalesData = (Map<String, Object>) tuple.getT6();
        Map<String, Object> medicalProfileData = (Map<String, Object>) tuple.getT7(); // NEW!

        return formatSmartWinMcpData(userData, clientData, salesStats, productsData,
                                   dashboardData, clientSalesData, medicalProfileData);
    });
```

**Step 6: Update Formatting Method**
```java
// UPDATE formatSmartWinMcpData method signature (line 503):
private String formatSmartWinMcpData(Map<String, Object> userData, Map<String, Object> clientData,
                                   Map<String, Object> salesStats, Map<String, Object> productsData,
                                   Map<String, Object> dashboardData, Map<String, Object> clientSalesData,
                                   Map<String, Object> medicalProfileData) { // NEW PARAMETER!

// ADD medical profile section (around line 520):
// Medical Profile Section
if (medicalProfileData != null && medicalProfileData.containsKey("medicalProfile")) {
    Map<String, Object> profile = (Map<String, Object>) medicalProfileData.get("medicalProfile");
    sb.append("🏥 PROFIL MÉDICAL PERSONNEL:\n");
    sb.append("  • Groupe sanguin: ").append(profile.get("bloodType")).append("\n");
    sb.append("  • Allergies: ").append(profile.get("allergies")).append("\n");
    sb.append("  • Conditions chroniques: ").append(profile.get("chronicConditions")).append("\n");
    sb.append("  • Contact d'urgence: ").append(profile.get("emergencyContact")).append("\n");
    sb.append("  • Téléphone urgence: ").append(profile.get("emergencyPhone")).append("\n");
    sb.append("  • Numéro assurance: ").append(profile.get("insuranceNumber")).append("\n");
    sb.append("  • Médecin traitant: ").append(profile.get("doctorName")).append("\n");
    sb.append("  • Téléphone médecin: ").append(profile.get("doctorPhone")).append("\n\n");
}
```

**Step 7: Test Questions**
```
"quel est mon groupe sanguin?"
"quelles sont mes allergies?"
"qui est mon contact d'urgence?"
"quel est mon numéro d'assurance?"
"qui est mon médecin traitant?"
"mon profil médical complet?"
"mes conditions chroniques?"
```

**Expected AI Response:**
```
🏥 PROFIL MÉDICAL PERSONNEL:
  • Groupe sanguin: O+
  • Allergies: Pénicilline, Aspirine
  • Conditions chroniques: Hypertension, Diabète Type 2
  • Contact d'urgence: Fatima Benali (épouse)
  • Téléphone urgence: +212 6 87 65 43 21
  • Numéro assurance: CNSS-123456789
  • Médecin traitant: Dr. Hassan Alami
  • Téléphone médecin: +212 5 22 34 56 78
```

### **🎯 Key Success Factors:**

1. **🔄 Reactive Pattern:** Always add new Mono → Update Zip → Update Formatting
2. **🧠 AI Intelligence:** No hardcoded keywords needed - AI understands automatically
3. **⚡ Performance:** Parallel fetching maintains speed regardless of data sources
4. **🛡️ Error Handling:** Each data source fails independently with onErrorReturn()
5. **🎯 Comprehensive Context:** AI gets complete picture for intelligent responses

This approach makes the MCP system **infinitely extensible** while maintaining **maximum performance**! 🚀
