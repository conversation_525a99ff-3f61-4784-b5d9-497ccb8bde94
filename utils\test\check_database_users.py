#!/usr/bin/env python3
"""
Check what users actually exist in both databases
"""

import requests
import json

def check_chat_mcp_users():
    """Check users in Chat-MCP database"""
    print("🔍 Checking Chat-MCP Database Users...")
    
    # Try to login with different users to see which ones exist
    test_users = ["user1", "user2", "user3", "user4", "admin"]
    existing_users = []
    
    for username in test_users:
        url = "http://localhost:8080/api/auth/login"
        payload = {
            "username": username,
            "password": "password"
        }
        
        try:
            response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})
            if response.status_code == 200:
                data = response.json()
                user_info = {
                    'username': data.get('username'),
                    'userId': data.get('userId'),
                    'email': data.get('email')
                }
                existing_users.append(user_info)
                print(f"   ✅ {username} - ID: {user_info['userId']}, Email: {user_info['email']}")
            else:
                print(f"   ❌ {username} - Not found")
        except Exception as e:
            print(f"   ❌ {username} - Error: {e}")
    
    return existing_users

def check_win_mcp_users():
    """Check users in Win-MCP database"""
    print("\n🔍 Checking Win-MCP Database Users...")
    
    # Get tenant token first
    tenant_url = "http://localhost:8082/auth/tenant/login"
    tenant_payload = {
        "username": "0001",
        "password": "123456"
    }
    
    try:
        tenant_response = requests.post(tenant_url, json=tenant_payload, headers={'Content-Type': 'application/json'})
        if tenant_response.status_code != 200:
            print("   ❌ Cannot get tenant token")
            return []
        
        tenant_data = tenant_response.json()
        tenant_token = tenant_data.get('token')
        print(f"   🏥 Tenant token obtained")
        
        # Try to login with different users
        test_users = ["user1", "user2", "user3", "user4", "admin", "0001"]
        existing_users = []
        
        for username in test_users:
            user_url = "http://localhost:8082/auth/login"
            user_payload = {
                "username": username,
                "password": "password" if username != "0001" else "123456"
            }
            user_headers = {
                'Content-Type': 'application/json',
                'AuthorizationTenant': f'BearerTenant {tenant_token}'
            }
            
            try:
                user_response = requests.post(user_url, json=user_payload, headers=user_headers)
                if user_response.status_code == 200:
                    data = user_response.json()
                    user_info = {
                        'username': data.get('username'),
                        'userId': data.get('userId'),
                        'email': data.get('email')
                    }
                    existing_users.append(user_info)
                    print(f"   ✅ {username} - ID: {user_info['userId']}, Email: {user_info['email']}")
                else:
                    print(f"   ❌ {username} - Not found")
            except Exception as e:
                print(f"   ❌ {username} - Error: {e}")
        
        return existing_users
        
    except Exception as e:
        print(f"   ❌ Error getting tenant token: {e}")
        return []

def check_medical_profiles():
    """Check which users have medical profiles"""
    print("\n🩺 Checking Medical Profiles...")
    
    # Get authentication tokens
    tenant_url = "http://localhost:8082/auth/tenant/login"
    tenant_payload = {
        "username": "0001",
        "password": "123456"
    }
    
    try:
        tenant_response = requests.post(tenant_url, json=tenant_payload, headers={'Content-Type': 'application/json'})
        if tenant_response.status_code != 200:
            print("   ❌ Cannot get tenant token")
            return
        
        tenant_data = tenant_response.json()
        tenant_token = tenant_data.get('token')
        
        # Check medical profiles for existing users
        test_users = ["user1", "user2", "user3", "user4"]
        
        for username in test_users:
            # First authenticate user
            user_url = "http://localhost:8082/auth/login"
            user_payload = {
                "username": username,
                "password": "password"
            }
            user_headers = {
                'Content-Type': 'application/json',
                'AuthorizationTenant': f'BearerTenant {tenant_token}'
            }
            
            user_response = requests.post(user_url, json=user_payload, headers=user_headers)
            if user_response.status_code != 200:
                print(f"   ❌ {username} - Cannot authenticate")
                continue
            
            user_data = user_response.json()
            user_token = user_data.get('token')
            
            # Check medical profile
            medical_url = f"http://localhost:8082/api/winplus/user-data/{username}"
            medical_headers = {
                'AuthorizationTenant': f'BearerTenant {tenant_token}',
                'Authorization': f'Bearer {user_token}'
            }
            
            try:
                medical_response = requests.get(medical_url, headers=medical_headers)
                if medical_response.status_code == 200:
                    medical_data = medical_response.json()
                    if medical_data.get('medicalProfile'):
                        profile = medical_data['medicalProfile']
                        print(f"   ✅ {username} - Blood Type: {profile.get('bloodType')}, Allergies: {profile.get('allergies', 'None')[:30]}...")
                    else:
                        print(f"   ⚠️  {username} - User exists but no medical profile")
                else:
                    print(f"   ❌ {username} - Medical API error: {medical_response.status_code}")
            except Exception as e:
                print(f"   ❌ {username} - Error: {str(e)[:50]}...")
                
    except Exception as e:
        print(f"   ❌ Error: {e}")

def main():
    print("🔍 DATABASE USERS VERIFICATION")
    print("=" * 50)
    
    # Check both databases
    chat_users = check_chat_mcp_users()
    win_users = check_win_mcp_users()
    
    # Check medical profiles
    check_medical_profiles()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    print(f"Chat-MCP Users: {len(chat_users)} found")
    for user in chat_users:
        print(f"   - {user['username']} (ID: {user['userId']})")
    
    print(f"\nWin-MCP Users: {len(win_users)} found")
    for user in win_users:
        print(f"   - {user['username']} (ID: {user['userId']})")
    
    print(f"\n🎯 WORKING LOGIN CREDENTIALS:")
    print(f"=" * 30)
    
    # Find common users
    chat_usernames = {user['username'] for user in chat_users}
    win_usernames = {user['username'] for user in win_users if user['username'] != '0001'}
    common_users = chat_usernames.intersection(win_usernames)
    
    if common_users:
        print(f"✅ Users that work on BOTH backends:")
        for username in sorted(common_users):
            print(f"   Username: {username}")
            print(f"   Password: password")
            print()
    else:
        print(f"❌ No users work on both backends!")
    
    # Win-MCP only users
    win_only = win_usernames - chat_usernames
    if win_only:
        print(f"🏥 Users that work ONLY on Win-MCP:")
        for username in sorted(win_only):
            print(f"   Username: {username}")
            print(f"   Password: password")
            print()
    
    # Chat-MCP only users
    chat_only = chat_usernames - win_usernames
    if chat_only:
        print(f"💬 Users that work ONLY on Chat-MCP:")
        for username in sorted(chat_only):
            print(f"   Username: {username}")
            print(f"   Password: password")
            print()

if __name__ == "__main__":
    main()
