#!/usr/bin/env python3
"""
Test what data is actually available in Win-MCP for user1
"""

import requests
import json

def get_auth_tokens():
    """Get authentication tokens for Win-MCP"""
    # Tenant authentication
    tenant_response = requests.post(
        "http://localhost:8082/auth/tenant/login",
        json={"username": "0001", "password": "123456"},
        headers={'Content-Type': 'application/json'}
    )
    
    if tenant_response.status_code != 200:
        print(f"❌ Tenant auth failed: {tenant_response.text}")
        return None, None
    
    tenant_token = tenant_response.json().get('token')
    
    # User authentication
    user_response = requests.post(
        "http://localhost:8082/auth/login",
        json={"username": "user1", "password": "password"},
        headers={
            'Content-Type': 'application/json',
            'AuthorizationTenant': f'BearerTenant {tenant_token}'
        }
    )
    
    if user_response.status_code != 200:
        print(f"❌ User auth failed: {user_response.text}")
        return None, None
    
    user_token = user_response.json().get('token')
    return tenant_token, user_token

def test_available_data():
    """Test what data is available for user1"""
    print("🔍 TESTING WIN-MCP DATA AVAILABILITY FOR USER1")
    print("=" * 60)
    
    tenant_token, user_token = get_auth_tokens()
    if not tenant_token or not user_token:
        print("❌ Authentication failed")
        return
    
    print("✅ Authentication successful")
    
    headers = {
        'AuthorizationTenant': f'BearerTenant {tenant_token}',
        'Authorization': f'Bearer {user_token}'
    }
    
    # Test different endpoints
    endpoints = [
        ("/api/winplus/user-data/user1", "User Data"),
        ("/api/winplus/medical-profile/user1", "Medical Profile"),
        ("/api/winplus/dashboard/summary", "Dashboard Summary"),
        ("/api/winplus/clients", "Clients List"),
        ("/api/winplus/products", "Products List"),
        ("/api/winplus/sales", "Sales List"),
        ("/api/winplus/suppliers", "Suppliers List")
    ]
    
    for endpoint, name in endpoints:
        print(f"\n🔍 Testing {name}: {endpoint}")
        try:
            response = requests.get(f"http://localhost:8082{endpoint}", headers=headers)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, dict):
                        print(f"   ✅ Response keys: {list(data.keys())}")
                        
                        # Show specific data for important endpoints
                        if "medical-profile" in endpoint:
                            if data.get('medicalProfile'):
                                profile = data['medicalProfile']
                                print(f"   📋 Blood Type: {profile.get('bloodType')}")
                                print(f"   📋 Doctor: {profile.get('doctorName')}")
                        
                        elif "user-data" in endpoint:
                            if data.get('client'):
                                client = data['client']
                                print(f"   👤 Client Name: {client.get('nom')} {client.get('prenom')}")
                                print(f"   👤 Client Code: {client.get('codeClient')}")
                            if data.get('recentSales'):
                                sales = data['recentSales']
                                print(f"   💰 Recent Sales: {len(sales) if sales else 0}")
                        
                        elif "dashboard" in endpoint:
                            if data.get('clients'):
                                clients = data['clients']
                                print(f"   👥 Total Clients: {clients.get('total', 0)}")
                            if data.get('sales'):
                                sales = data['sales']
                                print(f"   💰 Sales Last 30 Days: {sales.get('last30Days', 0)}")
                        
                        elif "clients" in endpoint:
                            if isinstance(data, list):
                                print(f"   👥 Number of clients: {len(data)}")
                                if data:
                                    first_client = data[0]
                                    print(f"   👤 First client: {first_client.get('nom')} {first_client.get('prenom')}")
                        
                        elif "products" in endpoint:
                            if isinstance(data, list):
                                print(f"   📦 Number of products: {len(data)}")
                                if data:
                                    first_product = data[0]
                                    print(f"   📦 First product: {first_product.get('designation')}")
                        
                        elif "sales" in endpoint:
                            if isinstance(data, list):
                                print(f"   💰 Number of sales: {len(data)}")
                                if data:
                                    first_sale = data[0]
                                    print(f"   💰 First sale: {first_sale.get('numVente')} - {first_sale.get('mntNetTtc')}")
                    
                    elif isinstance(data, list):
                        print(f"   ✅ List with {len(data)} items")
                        if data and isinstance(data[0], dict):
                            print(f"   📋 Item keys: {list(data[0].keys())}")
                    
                except json.JSONDecodeError:
                    print(f"   ⚠️  Response is not JSON")
                except Exception as e:
                    print(f"   ⚠️  Error parsing response: {e}")
            else:
                print(f"   ❌ Failed: {response.text[:100]}...")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_mcp_microservice():
    """Test MCP microservice with medical questions"""
    print(f"\n🤖 TESTING MCP MICROSERVICE")
    print("=" * 40)
    
    questions = [
        "quelles sont mes informations médicales?",
        "quel est mon groupe sanguin?",
        "qui est mon médecin?",
        "quelles sont mes allergies?",
        "quels sont mes médicaments actuels?"
    ]
    
    for question in questions:
        print(f"\n❓ Question: {question}")
        
        try:
            response = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": "test-medical",
                    "content": question,
                    "username": "user1"
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                content = data.get('content', '')
                print(f"   💬 Response: {content[:100]}...")
            else:
                print(f"   ❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    test_available_data()
    test_mcp_microservice()

if __name__ == "__main__":
    main()
