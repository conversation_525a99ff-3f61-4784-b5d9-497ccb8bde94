package com.example.mcp_microservice_chatboot_ai.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for handling Win-MCP dual authentication (Tenant + User)
 * This service manages both tenant authorization (BearerTenant) and user authorization (Bearer)
 */
@Service
public class WinMcpAuthService {

    @Value("${win-mcp.api.url}")
    private String winMcpApiUrl;

    @Value("${win-mcp.api.auth-endpoint}")
    private String authEndpoint;

    @Value("${win-mcp.api.tenant-auth-endpoint}")
    private String tenantAuthEndpoint;

    @Value("${win-mcp.auth.type:DUAL}")
    private String authType;

    @Value("${win-mcp.auth.tenant.username:0001}")
    private String tenantUsername;

    @Value("${win-mcp.auth.tenant.password:123456}")
    private String tenantPassword;

    private final WebClient webClient;
    private final Map<String, String> userTokenCache = new ConcurrentHashMap<>();
    private String tenantToken = null;

    public WinMcpAuthService() {
        this.webClient = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    /**
     * Authentication result containing both tenant and user tokens
     */
    public static class WinMcpAuthResult {
        private final String tenantToken;
        private final String userToken;
        private final boolean isDualAuth;

        public WinMcpAuthResult(String tenantToken, String userToken, boolean isDualAuth) {
            this.tenantToken = tenantToken;
            this.userToken = userToken;
            this.isDualAuth = isDualAuth;
        }

        public String getTenantToken() { return tenantToken; }
        public String getUserToken() { return userToken; }
        public boolean isDualAuth() { return isDualAuth; }

        public Map<String, String> getHeaders() {
            Map<String, String> headers = new HashMap<>();
            if (isDualAuth && tenantToken != null) {
                headers.put("AuthorizationTenant", "BearerTenant " + tenantToken);
            }
            if (userToken != null) {
                headers.put("Authorization", "Bearer " + userToken);
            }
            return headers;
        }
    }

    /**
     * Authenticates user with Win-MCP using the configured authentication type
     * @param username The username to authenticate
     * @return WinMcpAuthResult containing the authentication tokens
     */
    public Mono<WinMcpAuthResult> authenticateUser(String username) {
        if ("DUAL".equalsIgnoreCase(authType)) {
            return authenticateWithDualAuth(username);
        } else {
            return authenticateWithSingleAuth(username);
        }
    }

    /**
     * Dual authentication: First authenticate tenant, then user
     */
    private Mono<WinMcpAuthResult> authenticateWithDualAuth(String username) {
        return authenticateTenant()
                .flatMap(tenantToken -> authenticateUserWithTenant(username, tenantToken)
                        .map(userToken -> new WinMcpAuthResult(tenantToken, userToken, true)))
                .doOnSuccess(result -> System.out.println("🔑 WIN-MCP DUAL AUTH: Successfully authenticated tenant and user: " + username))
                .doOnError(error -> System.out.println("❌ WIN-MCP DUAL AUTH: Authentication failed for user: " + username + ", error: " + error.getMessage()));
    }

    /**
     * Single authentication: Only authenticate user (like Chat-MCP)
     */
    private Mono<WinMcpAuthResult> authenticateWithSingleAuth(String username) {
        return authenticateUserOnly(username)
                .map(userToken -> new WinMcpAuthResult(null, userToken, false))
                .doOnSuccess(result -> System.out.println("🔑 WIN-MCP SINGLE AUTH: Successfully authenticated user: " + username))
                .doOnError(error -> System.out.println("❌ WIN-MCP SINGLE AUTH: Authentication failed for user: " + username + ", error: " + error.getMessage()));
    }

    /**
     * Authenticate tenant (pharmacy) to get tenant token
     */
    private Mono<String> authenticateTenant() {
        // Check if we already have a cached tenant token
        if (tenantToken != null) {
            return Mono.just(tenantToken);
        }

        Map<String, String> tenantAuthRequest = new HashMap<>();
        tenantAuthRequest.put("username", tenantUsername);
        tenantAuthRequest.put("password", tenantPassword);

        return webClient.post()
                .uri(winMcpApiUrl + tenantAuthEndpoint)
                .bodyValue(tenantAuthRequest)
                .retrieve()
                .bodyToMono(Map.class)
                .map(response -> {
                    String token = (String) response.get("token");
                    if (token != null) {
                        this.tenantToken = token; // Cache the tenant token
                    }
                    return token;
                })
                .timeout(Duration.ofSeconds(10))
                .doOnSuccess(token -> System.out.println("🏥 WIN-MCP: Tenant authentication successful"))
                .doOnError(error -> System.out.println("❌ WIN-MCP: Tenant authentication failed: " + error.getMessage()));
    }

    /**
     * Authenticate user with tenant token in header
     */
    private Mono<String> authenticateUserWithTenant(String username, String tenantToken) {
        // Check cache first
        String cachedToken = userTokenCache.get(username);
        if (cachedToken != null) {
            return Mono.just(cachedToken);
        }

        Map<String, String> userAuthRequest = new HashMap<>();
        userAuthRequest.put("username", username);
        userAuthRequest.put("password", "password"); // Default password for demo

        return webClient.post()
                .uri(winMcpApiUrl + authEndpoint)
                .header("AuthorizationTenant", "BearerTenant " + tenantToken)
                .bodyValue(userAuthRequest)
                .retrieve()
                .bodyToMono(Map.class)
                .map(response -> {
                    String token = (String) response.get("token");
                    if (token != null) {
                        userTokenCache.put(username, token); // Cache the user token
                    }
                    return token;
                })
                .timeout(Duration.ofSeconds(10))
                .doOnSuccess(token -> System.out.println("👤 WIN-MCP: User authentication successful for: " + username))
                .doOnError(error -> System.out.println("❌ WIN-MCP: User authentication failed for: " + username + ", error: " + error.getMessage()));
    }

    /**
     * Authenticate user only (single auth mode)
     */
    private Mono<String> authenticateUserOnly(String username) {
        // Check cache first
        String cachedToken = userTokenCache.get(username);
        if (cachedToken != null) {
            return Mono.just(cachedToken);
        }

        Map<String, String> userAuthRequest = new HashMap<>();
        userAuthRequest.put("username", username);
        userAuthRequest.put("password", "password"); // Default password for demo

        return webClient.post()
                .uri(winMcpApiUrl + authEndpoint)
                .bodyValue(userAuthRequest)
                .retrieve()
                .bodyToMono(Map.class)
                .map(response -> {
                    String token = (String) response.get("token");
                    if (token != null) {
                        userTokenCache.put(username, token); // Cache the user token
                    }
                    return token;
                })
                .timeout(Duration.ofSeconds(10))
                .doOnSuccess(token -> System.out.println("👤 WIN-MCP: Single user authentication successful for: " + username))
                .doOnError(error -> System.out.println("❌ WIN-MCP: Single user authentication failed for: " + username + ", error: " + error.getMessage()));
    }

    /**
     * Clear cached tokens (useful for testing or token refresh)
     */
    public void clearTokenCache() {
        userTokenCache.clear();
        tenantToken = null;
        System.out.println("🧹 WIN-MCP: Token cache cleared");
    }

    /**
     * Get authentication type
     */
    public String getAuthType() {
        return authType;
    }
}
