# 🚀 Quick Test Questions - MCP System

## 📋 **TOP 20 ESSENTIAL QUESTIONS TO TEST**

### 👤 **Personal Information (Must Test)**
1. `Quel est mon nom ?` → Should return: **Test User1**
2. `Quelle est mon adresse email ?` → Should return: **<EMAIL>**
3. `Où je travaille ?` → Should return: **Pharmacie Centrale**
4. `Quel est mon métier ?` → Should return: **Pharmacien**
5. `Quelle est mon adresse ?` → Should return: **123 Rue de la Santé**

### 💰 **Financial Information (Must Test)**
6. `Quel est mon solde ?` → Should return: **1250.0 €**
7. `Quelle est ma limite de crédit ?` → Should return: **5000.0 €**
8. `Quelle est ma méthode de paiement ?` → Should return: **Credit Card**
9. `Quand expire ma carte ?` → Should return: **12/2026**

### 📄 **Subscription Information (Must Test)**
10. `Quel est mon statut d'abonnement ?` → Should return: **Active**
11. `Combien coûte mon abonnement ?` → Should return: **499.00€**
12. `Quel type d'abonnement j'ai ?` → Should return: **Annual**

### 🧾 **Data Lists (Must Test)**
13. `Quelles sont mes dernières transactions ?` → Should return: **Real transaction list**
14. `Quelles sont mes factures ?` → Should return: **Real invoice list**
15. `Quelles sont mes commandes ?` → Should return: **Real order list**

### 📊 **Account Information (Must Test)**
16. `Quel type de compte j'ai ?` → Should return: **Professional**
17. `Dans quelle ville j'habite ?` → Should return: **Paris**
18. `Quel est mon fuseau horaire ?` → Should return: **Europe/Paris**

### 🔍 **Complex Questions (Must Test)**
19. `Résumé de mon compte ?` → Should return: **Complete profile summary**
20. `Toutes mes informations ?` → Should return: **All user data**

---

## 🎯 **QUICK TESTING PROCEDURE**

### Step 1: Start Services
```bash
# Terminal 1: Start Chat-MCP Backend
cd chat-mcp
.\mvnw.cmd spring-boot:run

# Terminal 2: Start MCP Microservice  
cd mcp_microservice_chatboot_ai
.\mvnw.cmd spring-boot:run
```

### Step 2: Test with PowerShell
```powershell
# Ask a question
Invoke-RestMethod -Uri 'http://localhost:8081/api/chat' -Method POST -ContentType 'application/json' -Body '{"content": "Quel est mon nom?", "conversationId": "test123", "username": "user1"}'

# Choose database option (always choose 1)
Invoke-RestMethod -Uri 'http://localhost:8081/api/chat' -Method POST -ContentType 'application/json' -Body '{"content": "1", "conversationId": "test123", "username": "user1"}'
```

### Step 3: Run Full Test Suite
```bash
# Run the complete test script
.\run_mcp_tests.bat
```

---

## ✅ **EXPECTED REAL DATA VALUES**

| Question | Expected Answer |
|----------|----------------|
| **Name** | Test User1 |
| **Email** | <EMAIL> |
| **Balance** | 1250.0 € |
| **Company** | Pharmacie Centrale |
| **Job Title** | Pharmacien |
| **Address** | 123 Rue de la Santé |
| **City** | Paris |
| **Subscription** | Active |
| **Account Type** | Professional |
| **Credit Limit** | 5000.0 € |

---

## 🚫 **WHAT NOT TO SEE (Old Mock Data)**

If you see these values, mock data is still being used:
- ❌ "Jean Dupont" or "Jean Dupont 2"
- ❌ "Facture #1234500A"
- ❌ Mock transaction IDs
- ❌ Hardcoded mock addresses

---

## 🎉 **SUCCESS INDICATORS**

✅ **Real Data Integration Working:**
- Name shows as "Test User1"
- Balance shows as "1250.0 €"
- Company shows as "Pharmacie Centrale"
- All data comes from H2 database
- No mock data fallback

✅ **Source Selection Working:**
- System asks "1️⃣ La base de données MCP" or "2️⃣ Mes connaissances générales"
- Option 1 returns real database data
- Option 2 returns general AI knowledge

✅ **Friendly Message Detection Working:**
- "Bonjour", "Salut", "Hello" don't ask for source selection
- Direct friendly responses without database queries

---

## 🔧 **TROUBLESHOOTING**

### If you see mock data:
1. Check if both services are running
2. Verify H2 database has real data
3. Check logs for authentication errors
4. Restart both services

### If authentication fails:
1. Check chat-mcp backend logs
2. Verify user1 exists in database
3. Check JWT token generation

### If no response:
1. Check service ports (8080, 8081)
2. Verify network connectivity
3. Check for compilation errors

---

**🎯 Use these 20 questions to quickly verify your MCP system is working with real database data!**
