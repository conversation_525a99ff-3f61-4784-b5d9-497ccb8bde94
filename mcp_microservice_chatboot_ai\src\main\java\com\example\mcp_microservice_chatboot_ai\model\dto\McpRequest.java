package com.example.mcp_microservice_chatboot_ai.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for MCP (Model Context Protocol) requests.
 * Simple request format for smart MCP endpoints.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpRequest {
    
    /**
     * The username of the user making the request.
     */
    private String username;
    
    /**
     * The question or message content.
     */
    private String question;
    
    /**
     * Convert this McpRequest to a ChatRequest for internal processing.
     * 
     * @return A ChatRequest with the appropriate fields set
     */
    public ChatRequest toChatRequest() {
        return ChatRequest.builder()
                .username(this.username)
                .content(this.question)
                .conversationId("mcp-" + System.currentTimeMillis()) // Generate a simple conversation ID
                .build();
    }
}
