#!/usr/bin/env python3
"""
Test the fixed MCP microservice with medical questions
"""

import requests
import json

def test_medical_questions():
    """Test medical questions with the fixed MCP microservice"""
    print("🩺 TESTING FIXED MCP MICROSERVICE - MEDICAL QUESTIONS")
    print("=" * 60)
    
    medical_questions = [
        "quel est mon groupe sanguin?",
        "qui est mon médecin?", 
        "quelles sont mes allergies?",
        "quels sont mes médicaments actuels?",
        "quelles sont mes informations médicales?",
        "quelle est ma taille et mon poids?",
        "quel est mon numéro d'assurance?",
        "quand était mon dernier check-up?"
    ]
    
    for i, question in enumerate(medical_questions, 1):
        print(f"\n🔍 Test {i}: {question}")
        
        try:
            # Send the question
            response = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": f"test-medical-{i}",
                    "content": question,
                    "username": "user1"
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                content = data.get('content', '')
                print(f"   💬 Response: {content[:150]}...")
                
                # If it asks for source selection, choose option 1 (database)
                if "Souhaitez-vous que je vous réponde" in content:
                    print(f"   🔄 Selecting database option...")
                    
                    # Send option 1
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": f"test-medical-{i}",
                            "content": "1",
                            "username": "user1"
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   ✅ Final Response: {content2[:200]}...")
                        
                        # Check if we got actual medical data
                        if any(keyword in content2.lower() for keyword in ['o+', 'dr.', 'hassan', 'alami', 'groupe sanguin', 'médecin']):
                            print(f"   🎉 SUCCESS: Got medical data!")
                        elif "je n'ai pas trouvé" in content2.lower():
                            print(f"   ❌ FAILED: Still getting 'info not found'")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                    else:
                        print(f"   ❌ Option selection failed: {response2.text}")
                        
            else:
                print(f"   ❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_dashboard_questions():
    """Test dashboard/summary questions"""
    print(f"\n📊 TESTING DASHBOARD QUESTIONS")
    print("=" * 40)
    
    dashboard_questions = [
        "combien de clients avons-nous?",
        "quel est le résumé du tableau de bord?",
        "quelles sont les ventes des 30 derniers jours?"
    ]
    
    for i, question in enumerate(dashboard_questions, 1):
        print(f"\n🔍 Test {i}: {question}")
        
        try:
            response = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": f"test-dashboard-{i}",
                    "content": question,
                    "username": "user1"
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                content = data.get('content', '')
                
                if "Souhaitez-vous que je vous réponde" in content:
                    # Send option 1
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": f"test-dashboard-{i}",
                            "content": "1",
                            "username": "user1"
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   💬 Response: {content2[:200]}...")
                        
                        # Check for dashboard data
                        if any(keyword in content2.lower() for keyword in ['4 clients', '2768', 'tableau de bord']):
                            print(f"   🎉 SUCCESS: Got dashboard data!")
                        elif "je n'ai pas trouvé" in content2.lower():
                            print(f"   ❌ FAILED: Still getting 'info not found'")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                else:
                    print(f"   💬 Direct Response: {content[:200]}...")
                        
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    test_medical_questions()
    test_dashboard_questions()
    
    print(f"\n" + "=" * 60)
    print(f"🎯 SUMMARY")
    print(f"=" * 60)
    print(f"✅ Fixed endpoints: Medical Profile, Dashboard Summary")
    print(f"❌ Disabled endpoints: User Data, Clients, Sales, Products (due to issues)")
    print(f"🎯 Expected: Medical questions should now work!")
    print(f"🎯 Expected: Dashboard questions should work!")

if __name__ == "__main__":
    main()
