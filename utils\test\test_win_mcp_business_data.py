#!/usr/bin/env python3
"""
Test Win-MCP business data questions (clients, sales, invoices, products)
"""

import requests
import json
import time

def test_win_mcp_business_questions():
    """Test Win-MCP business data questions"""
    print("🏢 TESTING WIN-MCP BUSINESS DATA QUESTIONS")
    print("=" * 70)
    
    questions = [
        ("my clients?", "Should get clients database from Win-MCP"),
        ("mes clients?", "Should get clients database from Win-MCP"),
        ("my invoices?", "Should get sales/invoices data from Win-MCP"),
        ("mes factures?", "Should get sales/invoices data from Win-MCP"),
        ("what products are available?", "Should get products catalog from Win-MCP"),
        ("quels produits sont disponibles?", "Should get products catalog from Win-MCP"),
        ("sales statistics?", "Should get sales statistics from Win-MCP"),
        ("statistiques de vente?", "Should get sales statistics from Win-MCP"),
        ("dashboard summary?", "Should get dashboard data from Win-MCP"),
        ("résumé du tableau de bord?", "Should get dashboard data from Win-MCP")
    ]
    
    for i, (question, expected) in enumerate(questions, 1):
        print(f"\n🧪 Test {i}: {question}")
        print(f"   Expected: {expected}")
        
        conversation_id = f"test-win-mcp-business-{i}-{int(time.time())}"
        username = "user1"
        backend = "win-mcp"
        
        try:
            # Step 1: Send the question with backend selection
            response1 = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": conversation_id,
                    "content": question,
                    "username": username,
                    "backend": backend
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Step 1 Status: {response1.status_code}")
            if response1.status_code == 200:
                data1 = response1.json()
                content1 = data1.get('content', '')
                print(f"   Step 1 Response: {content1[:100]}...")
                
                # Step 2: Select database option if prompted
                if "Souhaitez-vous que je vous réponde" in content1:
                    print(f"   🔄 Step 2: Selecting database option...")
                    
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": conversation_id,
                            "content": "1",
                            "username": username,
                            "backend": backend
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    print(f"   Step 2 Status: {response2.status_code}")
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   Step 2 Response: {content2[:300]}...")
                        
                        # Analyze the response for business data
                        if "je n'ai pas trouvé" in content2.lower():
                            print(f"   ❌ FAILED: Still getting 'info not found'")
                        elif any(keyword in content2.lower() for keyword in ['clients', 'ventes', 'produits', 'factures', 'dh', 'code client', 'tableau de bord']):
                            print(f"   🎉 SUCCESS: Got Win-MCP business data!")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                    else:
                        print(f"   ❌ Step 2 failed: {response2.text}")
                else:
                    print(f"   ⚠️  No source selection prompt")
                    
            else:
                print(f"   ❌ Step 1 failed: {response1.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_specific_business_scenarios():
    """Test specific business scenarios"""
    print(f"\n🎯 TESTING SPECIFIC BUSINESS SCENARIOS")
    print("=" * 60)
    
    scenarios = [
        ("How many clients do we have?", "Should count clients from database"),
        ("Combien de clients avons-nous?", "Should count clients from database"),
        ("What is the total sales amount?", "Should calculate total sales"),
        ("Quel est le montant total des ventes?", "Should calculate total sales"),
        ("Which products are most expensive?", "Should analyze product prices"),
        ("Quels produits sont les plus chers?", "Should analyze product prices"),
        ("Show me client statistics", "Should show client analytics"),
        ("Montrez-moi les statistiques clients", "Should show client analytics")
    ]
    
    for i, (question, expected) in enumerate(scenarios, 1):
        print(f"\n🎯 Scenario {i}: {question}")
        print(f"   Expected: {expected}")
        
        conversation_id = f"test-scenario-{i}-{int(time.time())}"
        username = "user1"
        backend = "win-mcp"
        
        try:
            # Send the question
            response1 = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": conversation_id,
                    "content": question,
                    "username": username,
                    "backend": backend
                },
                headers={'Content-Type': 'application/json'}
            )
            
            if response1.status_code == 200:
                data1 = response1.json()
                content1 = data1.get('content', '')
                
                # Select database option if prompted
                if "Souhaitez-vous que je vous réponde" in content1:
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": conversation_id,
                            "content": "1",
                            "username": username,
                            "backend": backend
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   Response: {content2[:200]}...")
                        
                        # Check for business analytics
                        if "je n'ai pas trouvé" in content2.lower():
                            print(f"   ❌ FAILED: Getting 'info not found'")
                        elif any(keyword in content2.lower() for keyword in ['total', 'clients', 'statistiques', 'montant', 'prix', 'dh']):
                            print(f"   🎉 SUCCESS: Got business analytics!")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                            
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    print("🏢 TESTING WIN-MCP BUSINESS DATA")
    print("=" * 80)
    print("Testing Win-MCP business questions with enabled endpoints...")
    print("Expected to work now:")
    print("✅ Clients data: /api/winplus/clients")
    print("✅ Sales data: /api/winplus/ventes")
    print("✅ Products data: /api/winplus/produits")
    print("✅ Dashboard data: /api/winplus/dashboard/summary")
    
    # Test basic business questions
    test_win_mcp_business_questions()
    
    # Test specific business scenarios
    test_specific_business_scenarios()
    
    print(f"\n" + "=" * 80)
    print(f"🎯 WIN-MCP BUSINESS DATA TEST SUMMARY")
    print(f"=" * 80)
    print(f"✅ Applied fixes:")
    print(f"   - Enabled clients endpoint: /api/winplus/clients")
    print(f"   - Enabled client sales data: /api/winplus/ventes")
    print(f"   - Added comprehensive clients formatting")
    print(f"   - Enhanced business data processing")
    print(f"\n💡 EXPECTED RESULTS:")
    print(f"   - 'my clients?' should return client database")
    print(f"   - 'my invoices?' should return sales/invoices data")
    print(f"   - 'products available?' should return product catalog")
    print(f"   - Business analytics should work")
    print(f"\n🔍 CHECK MCP MICROSERVICE LOGS:")
    print(f"   Look for new debug messages:")
    print(f"   - '✅ Clients data fetched successfully'")
    print(f"   - '✅ Client sales data fetched successfully'")
    print(f"   - '🏢 BASE DE DONNÉES CLIENTS (X clients)'")
    print(f"   - '💰 VENTES DU CLIENT (X ventes)'")

if __name__ == "__main__":
    main()
