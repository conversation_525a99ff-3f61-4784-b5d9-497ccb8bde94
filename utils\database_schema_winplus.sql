-- Main entities

CREATE TABLE Depot (
  id INT PRIMARY KEY,
  codeDepot VARCHAR(50),
  libelleDepot VARCHAR(100),
  adr1 VARCHAR(255),
  adr2 VARCHAR(255),
  codePostal VARCHAR(20),
  primaire BOOLEAN,
  estActif BOOLEAN DEFAULT TRUE,
  userModifiable BOOLEAN,
  audited B<PERSON><PERSON>EAN
);

CREATE TABLE Operateur (
  id INT PRIMARY KEY,
  username VA<PERSON><PERSON><PERSON>(50),
  firstname VA<PERSON>HA<PERSON>(100),
  lastname VARCHAR(100),
  email VARCHAR(100),
  password VARCHAR(255),
  actif BOOLEAN,
  userModifiable BOOLEAN,
  audited BOOLEAN
);

CREATE TABLE ParametresUser (
  id INT PRIMARY KEY,
  operateur_id INT,
  plafondUser DECIMAL(15,2),
  typeControlHoraireLogin VARCHAR(50),
  typeControlLieuLogin VARCHAR(50),
  logoutAfterEachVente BOOLEAN,
  userModifiable BOOLEAN,
  audited B<PERSON><PERSON><PERSON>N,
  FOREIGN KEY (operateur_id) REFERENCES Operateur(id)
);

CREATE TABLE Produit (
  id INT PRIMARY KEY,
  codePrd VARCHAR(50),
  designation VARCHAR(255),
  codeBarre VARCHAR(50),
  prixAchatStd DECIMAL(15,2),
  prixAchatTtc DECIMAL(15,2),
  prixVenteStd DECIMAL(15,2),
  prixVenteTtc DECIMAL(15,2),
  prixFabHt DECIMAL(15,2),
  prixHosp DECIMAL(15,2),
  prixValoTtc DECIMAL(15,2),
  pphTtc DECIMAL(15,2),
  tauxRemb DECIMAL(5,2),
  totalStock DECIMAL(15,2),
  estStockable BOOLEAN,
  estVendable BOOLEAN,
  estPsychotrope BOOLEAN,
  estToxique BOOLEAN,
  estPrinceps BOOLEAN,
  estMarche BOOLEAN,
  estFabrique BOOLEAN,
  estPrixmarque BOOLEAN,
  estOblgPrescription BOOLEAN,
  estRbrsblBase BOOLEAN,
  estTpaBase BOOLEAN,
  typeProcess VARCHAR(50),
  userModifiable BOOLEAN,
  audited BOOLEAN,
  produitBase_id INT,
  categorie_id INT,
  forme_id INT,
  familleTarifaire_id INT,
  laboratoire_id INT,
  tva_id INT
);

CREATE TABLE ProduitBase (
  id INT PRIMARY KEY,
  nomRacine VARCHAR(255),
  psychotrope BOOLEAN,
  tableau VARCHAR(50),
  userModifiable BOOLEAN,
  audited BOOLEAN,
  atc_id INT,
  familleMedicamenteuse_id INT
);

CREATE TABLE Stock (
  id INT PRIMARY KEY,
  qteUnit DECIMAL(15,2),
  qteDelta DECIMAL(15,2),
  prixAchatTtc DECIMAL(15,2),
  prixVenteTtc DECIMAL(15,2),
  prixValoTtc DECIMAL(15,2),
  datePeremption DATE,
  numeroLot VARCHAR(100),
  stockIndicateur VARCHAR(50),
  flagEmpl VARCHAR(50),
  userModifiable BOOLEAN,
  audited BOOLEAN,
  produit_id INT,
  depot_id INT,
  FOREIGN KEY (produit_id) REFERENCES Produit(id),
  FOREIGN KEY (depot_id) REFERENCES Depot(id)
);

CREATE TABLE Fournisseur (
  id INT PRIMARY KEY,
  codeFrnsr VARCHAR(50),
  libelleFrnsr VARCHAR(255),
  userModifiable BOOLEAN,
  audited BOOLEAN
);

CREATE TABLE Client (
  id INT PRIMARY KEY,
  nomClient VARCHAR(255),
  soldeClientAvantVente DECIMAL(15,2),
  userModifiable BOOLEAN,
  audited BOOLEAN
);

CREATE TABLE Beneficiaire (
  id INT PRIMARY KEY,
  nomPatient VARCHAR(255),
  client_id INT,
  userModifiable BOOLEAN,
  audited BOOLEAN,
  FOREIGN KEY (client_id) REFERENCES Client(id)
);

CREATE TABLE EnteteVente (
  id INT PRIMARY KEY,
  numVente INT,
  numFacture INT,
  dateVente TIMESTAMP,
  heureVenteDebut TIME,
  heureVenteFin TIME,
  nomClient VARCHAR(255),
  nomPatient VARCHAR(255),
  mntBrutHt DECIMAL(15,2),
  mntBrutTtc DECIMAL(15,2),
  mntNetHt DECIMAL(15,2),
  mntNetTtc DECIMAL(15,2),
  mntRemiseHt DECIMAL(15,2),
  mntRemiseTtc DECIMAL(15,2),
  mntTva DECIMAL(15,2),
  mntEncaisse DECIMAL(15,2),
  mntVenteDu DECIMAL(15,2),
  tauxRemise DECIMAL(5,2),
  nbrLignes INT,
  nbrPrd INT,
  totalQte DECIMAL(15,2),
  resteAVentiler DECIMAL(15,2),
  maladieChronique BOOLEAN,
  statut VARCHAR(50),
  sousStatut VARCHAR(50),
  statutLivre VARCHAR(50),
  typeVente VARCHAR(50),
  typeOperation VARCHAR(50),
  typeEncaissement VARCHAR(50),
  typeAvoir VARCHAR(50),
  configurationGestionRemise VARCHAR(50),
  typeRemiseClient VARCHAR(50),
  statusEnc VARCHAR(50),
  totalMontantResteApayer DECIMAL(15,2),
  userModifiable BOOLEAN,
  audited BOOLEAN,
  client_id INT,
  patient_id INT,
  operateur_id INT,
  medecin_id INT,
  modeEncaissement_id INT,
  FOREIGN KEY (client_id) REFERENCES Client(id),
  FOREIGN KEY (patient_id) REFERENCES Beneficiaire(id),
  FOREIGN KEY (operateur_id) REFERENCES Operateur(id)
);

CREATE TABLE DetailVente (
  id INT PRIMARY KEY,
  qte DECIMAL(15,2),
  prixVenteTtc DECIMAL(15,2),
  mntRemiseTtc DECIMAL(15,2),
  mntNetTtc DECIMAL(15,2),
  enteteVente_id INT,
  produit_id INT,
  FOREIGN KEY (enteteVente_id) REFERENCES EnteteVente(id),
  FOREIGN KEY (produit_id) REFERENCES Produit(id)
);

CREATE TABLE EnteteBlAchat (
  id INT PRIMARY KEY,
  dateBla TIMESTAMP,
  dateCreation TIMESTAMP,
  dateFacture TIMESTAMP,
  mntBrutHt DECIMAL(15,2),
  mntBrutTtc DECIMAL(15,2),
  mntNetHt DECIMAL(15,2),
  mntNetTtc DECIMAL(15,2),
  mntRemiseHt DECIMAL(15,2),
  mntRemiseTtc DECIMAL(15,2),
  mntTva DECIMAL(15,2),
  mntAchatStd DECIMAL(15,2),
  mntVenteStd DECIMAL(15,2),
  nbrLigne INT,
  flagAccepteEcart BOOLEAN,
  userModifiable BOOLEAN,
  audited BOOLEAN,
  depot_id INT,
  fournisseur_id INT,
  devise_id INT,
  enteteCmdAchat_id INT,
  FOREIGN KEY (depot_id) REFERENCES Depot(id),
  FOREIGN KEY (fournisseur_id) REFERENCES Fournisseur(id)
);

CREATE TABLE DetailBlAchat (
  id INT PRIMARY KEY,
  qte DECIMAL(15,2),
  prixAchatTtc DECIMAL(15,2),
  enteteBlAchat_id INT,
  produit_id INT,
  FOREIGN KEY (enteteBlAchat_id) REFERENCES EnteteBlAchat(id),
  FOREIGN KEY (produit_id) REFERENCES Produit(id)
);

CREATE TABLE TransfertStock (
  id INT PRIMARY KEY,
  numTransfert INT,
  dateCreation TIMESTAMP,
  dateAnnulation TIMESTAMP,
  qteTotale DECIMAL(15,2),
  statut VARCHAR(50),
  zoneSource_id INT,
  zoneDestination_id INT,
  userCreation_id INT,
  userAnnulation_id INT,
  userValidation_id INT,
  FOREIGN KEY (zoneSource_id) REFERENCES Depot(id),
  FOREIGN KEY (zoneDestination_id) REFERENCES Depot(id),
  FOREIGN KEY (userCreation_id) REFERENCES Operateur(id),
  FOREIGN KEY (userAnnulation_id) REFERENCES Operateur(id),
  FOREIGN KEY (userValidation_id) REFERENCES Operateur(id)
);

CREATE TABLE ProduitQuantite (
  id INT PRIMARY KEY,
  quantite DECIMAL(15,2),
  transfertStock_id INT,
  produit_id INT,
  FOREIGN KEY (transfertStock_id) REFERENCES TransfertStock(id),
  FOREIGN KEY (produit_id) REFERENCES Produit(id)
);

CREATE TABLE InitStock (
  id INT PRIMARY KEY,
  dateCreation TIMESTAMP,
  dateAnnulation TIMESTAMP,
  dateValidation TIMESTAMP,
  mntPrixAchatTtc DECIMAL(15,2),
  mntPrixVenteTtc DECIMAL(15,2),
  nbrLignes INT,
  totalQte DECIMAL(15,2),
  statut VARCHAR(50),
  isLoadedFromFile BOOLEAN DEFAULT FALSE,
  depot_id INT,
  operateur_id INT,
  FOREIGN KEY (depot_id) REFERENCES Depot(id),
  FOREIGN KEY (operateur_id) REFERENCES Operateur(id)
);

CREATE TABLE InitStockDetails (
  id INT PRIMARY KEY,
  codePrd VARCHAR(50),
  dsgnPrd VARCHAR(255),
  numLot VARCHAR(100),
  datePeremption DATE,
  quantite DECIMAL(15,2),
  prixAchatTtc DECIMAL(15,2),
  prixVenteTtc DECIMAL(15,2),
  mntLigneAchatTtc DECIMAL(15,2),
  mntLigneVenteTtc DECIMAL(15,2),
  prixAchatStd DECIMAL(15,2),
  prixVenteStd DECIMAL(15,2),
  prixFabHt DECIMAL(15,2),
  prixHosp DECIMAL(15,2),
  pbrH DECIMAL(15,2),
  pbrP DECIMAL(15,2),
  tauxMarge DECIMAL(5,2),
  tauxTva DECIMAL(5,2),
  numLigne INT,
  codeBarre VARCHAR(50),
  valider BOOLEAN DEFAULT FALSE,
  initStock_id INT,
  produit_id INT,
  ctgr_id INT,
  frm_id INT,
  ft_id INT,
  labo_id INT,
  FOREIGN KEY (initStock_id) REFERENCES InitStock(id),
  FOREIGN KEY (produit_id) REFERENCES Produit(id)
);

CREATE TABLE EnteteInventaire (
  id INT PRIMARY KEY,
  numeroInventaire INT,
  dateCreation TIMESTAMP,
  dateInventaire TIMESTAMP,
  methode VARCHAR(50),
  statutInventaire VARCHAR(50),
  userModifiable BOOLEAN,
  audited BOOLEAN,
  depot_id INT,
  FOREIGN KEY (depot_id) REFERENCES Depot(id)
);

CREATE TABLE DetailInventaireListe (
  id INT PRIMARY KEY,
  qteTheorique DECIMAL(15,2),
  qteReelle DECIMAL(15,2),
  enteteInventaire_id INT,
  produit_id INT,
  FOREIGN KEY (enteteInventaire_id) REFERENCES EnteteInventaire(id),
  FOREIGN KEY (produit_id) REFERENCES Produit(id)
);

CREATE TABLE OrdreProduction (
  id INT PRIMARY KEY,
  dateDeb TIMESTAMP,
  dateFin TIMESTAMP,
  qteFab DECIMAL(15,2),
  userModifiable BOOLEAN,
  audited BOOLEAN,
  process_id INT,
  sortieDepot_id INT,
  FOREIGN KEY (sortieDepot_id) REFERENCES Depot(id)
);

CREATE TABLE ProcessSortie (
  id INT PRIMARY KEY,
  ordreProduction_id INT,
  FOREIGN KEY (ordreProduction_id) REFERENCES OrdreProduction(id)
);

CREATE TABLE Confrere (
  id INT PRIMARY KEY,
  nom VARCHAR(255),
  prenom VARCHAR(255),
  raisonSociale VARCHAR(255),
  nomComplet VARCHAR(255),
  adr1 VARCHAR(255),
  adr2 VARCHAR(255),
  numTelephone VARCHAR(50),
  gsm VARCHAR(50),
  email VARCHAR(100),
  numIce VARCHAR(50),
  solde DECIMAL(15,2),
  typeConfere VARCHAR(50),
  typeTiers VARCHAR(50),
  estActif BOOLEAN,
  userModifiable BOOLEAN,
  audited BOOLEAN,
  ville_id INT
);

CREATE TABLE EnteteEchange (
  id INT PRIMARY KEY,
  numEchange VARCHAR(50),
  dateEchange TIMESTAMP,
  nomPharmacien VARCHAR(255),
  raisonSociale VARCHAR(255),
  sensEchange VARCHAR(50),
  typePrix VARCHAR(50),
  tauxRemise DECIMAL(5,2),
  totalQtEchange DECIMAL(15,2),
  totalQtUg DECIMAL(15,2),
  totalMntEchangeHt DECIMAL(15,2),
  totalMntEchangeTtc DECIMAL(15,2),
  totalMntRemiseHt DECIMAL(15,2),
  totalMntRemiseTtc DECIMAL(15,2),
  totalPrixAchatStd DECIMAL(15,2),
  totalPrixVenteStd DECIMAL(15,2),
  resteSolde DECIMAL(15,2),
  statut VARCHAR(50),
  sousStatut VARCHAR(50),
  userModifiable BOOLEAN,
  audited BOOLEAN,
  confrere_id INT,
  operateur_id INT,
  FOREIGN KEY (confrere_id) REFERENCES Confrere(id),
  FOREIGN KEY (operateur_id) REFERENCES Operateur(id)
);

CREATE TABLE DetailEchangeProduit (
  id INT PRIMARY KEY,
  enteteEchange_id INT,
  produit_id INT,
  FOREIGN KEY (enteteEchange_id) REFERENCES EnteteEchange(id),
  FOREIGN KEY (produit_id) REFERENCES Produit(id)
);

CREATE TABLE AnneeComptable (
  id INT PRIMARY KEY,
  annee INT,
  dateCreation TIMESTAMP,
  dateOuverture TIMESTAMP,
  statut VARCHAR(2),
  tenantId INT,
  userOuverture_id INT,
  societeTenant_id INT,
  FOREIGN KEY (userOuverture_id) REFERENCES Operateur(id)
);

CREATE TABLE SecurityJournal (
  id INT PRIMARY KEY,
  logDate TIMESTAMP,
  srcIp VARCHAR(50),
  type VARCHAR(50),
  user_id INT,
  tenantPrincipal_id INT,
  FOREIGN KEY (user_id) REFERENCES Operateur(id)
);

CREATE TABLE BatchAdmin (
  id INT PRIMARY KEY,
  code VARCHAR(50),
  title VARCHAR(255),
  description TEXT,
  statut BOOLEAN
);

CREATE TABLE BatchConfigurationItem (
  id INT PRIMARY KEY,
  name VARCHAR(100),
  label VARCHAR(255),
  type VARCHAR(50),
  required BOOLEAN,
  batchAdmin_id INT,
  FOREIGN KEY (batchAdmin_id) REFERENCES BatchAdmin(id)
);

CREATE TABLE BatchConfigurationItemOption (
  id INT PRIMARY KEY,
  label VARCHAR(255),
  value VARCHAR(255),
  batchConfigurationItem_id INT,
  FOREIGN KEY (batchConfigurationItem_id) REFERENCES BatchConfigurationItem(id)
);