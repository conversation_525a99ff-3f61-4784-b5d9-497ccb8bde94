#!/usr/bin/env python3
"""
Test to verify that Win-MCP now returns user-specific data instead of generic data
This test compares responses for user1 vs user2 to ensure they get different data
"""

import requests
import json
import time

def test_user_specific_data(username):
    """Test user-specific data for a given username"""
    
    print(f"\n🔍 Testing user-specific data for: {username}")
    print("-" * 50)
    
    # Use the regular chat endpoint with proper conversation flow
    url = "http://localhost:8081/api/chat"
    conversation_id = f"test-{username}-{int(time.time())}"
    
    # Test client information
    print("📋 Step 1: Ask about client information")
    payload1 = {
        "conversationId": conversation_id,
        "username": username,
        "content": "my client information?",
        "backend": "WIN_MCP"
    }
    
    try:
        response1 = requests.post(url, json=payload1, timeout=30)
        if response1.status_code == 200:
            response1_data = response1.json()
            response1_text = response1_data.get('content', '')
            
            if "1️⃣" in response1_text and "2️⃣" in response1_text:
                print("✅ AI asking for source selection")
                
                # Select option 1 (database)
                print("📋 Step 2: Select option 1 (database)")
                payload2 = {
                    "conversationId": conversation_id,
                    "username": username, 
                    "content": "1",
                    "backend": "WIN_MCP"
                }
                
                response2 = requests.post(url, json=payload2, timeout=60)
                if response2.status_code == 200:
                    response2_data = response2.json()
                    response2_text = response2_data.get('content', '')
                    
                    print(f"✅ Response received for {username}")
                    
                    # Extract key information
                    user_info = {
                        'username': username,
                        'response_length': len(response2_text),
                        'contains_client_info': 'INFORMATIONS CLIENT' in response2_text,
                        'contains_sales_info': 'VENTES' in response2_text,
                        'response_snippet': response2_text[:300] + "..." if len(response2_text) > 300 else response2_text
                    }
                    
                    return user_info
                else:
                    print(f"❌ Option selection failed: {response2.status_code}")
                    return None
            else:
                print(f"⚠️  No option selection requested")
                return None
        else:
            print(f"❌ Initial request failed: {response1.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def compare_user_data():
    """Compare data between different users to verify user-specific responses"""
    
    print("🎯 TESTING USER-SPECIFIC DATA IN WIN-MCP")
    print("=" * 60)
    print("This test verifies that different users get different data")
    print("(Previously, all users were getting the same data)")
    print("=" * 60)
    
    # Test multiple users
    users = ["user1", "user2"]
    user_results = {}
    
    for username in users:
        result = test_user_specific_data(username)
        if result:
            user_results[username] = result
        time.sleep(2)  # Small delay between users
    
    # Compare results
    print("\n" + "=" * 60)
    print("📊 COMPARISON RESULTS:")
    print("=" * 60)
    
    if len(user_results) >= 2:
        user1_data = user_results.get('user1')
        user2_data = user_results.get('user2')
        
        if user1_data and user2_data:
            print(f"👤 User1 response length: {user1_data['response_length']} characters")
            print(f"👤 User2 response length: {user2_data['response_length']} characters")
            
            print(f"\n📋 User1 snippet:")
            print(f"   {user1_data['response_snippet']}")
            
            print(f"\n📋 User2 snippet:")
            print(f"   {user2_data['response_snippet']}")
            
            # Check if responses are different
            responses_different = user1_data['response_snippet'] != user2_data['response_snippet']
            
            print(f"\n🔍 ANALYSIS:")
            print(f"   Responses are different: {'✅ YES' if responses_different else '❌ NO'}")
            print(f"   Both contain client info: {'✅ YES' if user1_data['contains_client_info'] and user2_data['contains_client_info'] else '❌ NO'}")
            
            if responses_different:
                print(f"\n🎉 SUCCESS! Win-MCP is now returning USER-SPECIFIC data!")
                print("   ✅ Different users get different responses")
                print("   ✅ User-specific data filtering is working")
                print("   ✅ The issue has been FIXED!")
                return True
            else:
                print(f"\n⚠️  ISSUE STILL EXISTS: Users are getting identical data")
                print("   ❌ Same responses for different users")
                print("   ❌ User-specific filtering not working")
                return False
        else:
            print("❌ Could not get data for both users")
            return False
    else:
        print("❌ Could not test multiple users")
        return False

def test_sales_data_specificity():
    """Test that sales data is also user-specific"""
    
    print(f"\n🔍 TESTING SALES DATA SPECIFICITY")
    print("=" * 40)
    
    users = ["user1", "user2"]
    sales_results = {}
    
    for username in users:
        print(f"\n📊 Testing sales data for: {username}")
        
        url = "http://localhost:8081/api/chat"
        conversation_id = f"sales-{username}-{int(time.time())}"
        
        # Ask about sales
        payload1 = {
            "conversationId": conversation_id,
            "username": username,
            "content": "my sales data?",
            "backend": "WIN_MCP"
        }
        
        try:
            response1 = requests.post(url, json=payload1, timeout=30)
            if response1.status_code == 200:
                response1_data = response1.json()
                response1_text = response1_data.get('content', '')
                
                if "1️⃣" in response1_text:
                    # Select option 1
                    payload2 = {
                        "conversationId": conversation_id,
                        "username": username,
                        "content": "1",
                        "backend": "WIN_MCP"
                    }
                    
                    response2 = requests.post(url, json=payload2, timeout=60)
                    if response2.status_code == 200:
                        response2_data = response2.json()
                        response2_text = response2_data.get('content', '')
                        
                        sales_results[username] = {
                            'response': response2_text[:200] + "...",
                            'length': len(response2_text)
                        }
                        print(f"   ✅ Sales data received ({len(response2_text)} chars)")
                    
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
        
        time.sleep(1)
    
    # Compare sales results
    if len(sales_results) >= 2:
        user1_sales = sales_results.get('user1', {})
        user2_sales = sales_results.get('user2', {})
        
        if user1_sales and user2_sales:
            sales_different = user1_sales['response'] != user2_sales['response']
            print(f"\n📊 Sales data comparison:")
            print(f"   User1 sales length: {user1_sales['length']} chars")
            print(f"   User2 sales length: {user2_sales['length']} chars")
            print(f"   Sales data different: {'✅ YES' if sales_different else '❌ NO'}")
            
            return sales_different
    
    return False

if __name__ == "__main__":
    print("🚀 TESTING WIN-MCP USER-SPECIFIC DATA FIX")
    print("=" * 60)
    
    # Test client data specificity
    client_data_fixed = compare_user_data()
    
    # Test sales data specificity
    sales_data_fixed = test_sales_data_specificity()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS:")
    print("=" * 60)
    
    if client_data_fixed:
        print("✅ Client data: USER-SPECIFIC (FIXED)")
    else:
        print("❌ Client data: Still showing same data for all users")
    
    if sales_data_fixed:
        print("✅ Sales data: USER-SPECIFIC (FIXED)")
    else:
        print("❌ Sales data: Still showing same data for all users")
    
    if client_data_fixed and sales_data_fixed:
        print(f"\n🎉 CONGRATULATIONS!")
        print("🏆 WIN-MCP USER-SPECIFIC DATA ISSUE HAS BEEN FIXED!")
        print("✅ Different users now get different, personalized data")
        print("✅ Win-MCP now works like Chat-MCP with user-specific responses")
        print("✅ The data filtering is working correctly")
    elif client_data_fixed or sales_data_fixed:
        print(f"\n🔄 PARTIAL SUCCESS!")
        print("Some data types are now user-specific, but more work needed")
    else:
        print(f"\n⚠️  ISSUE STILL EXISTS")
        print("Users are still getting identical data - further investigation needed")
    
    print(f"\n📝 What was changed:")
    print("   ✅ Updated Smart Win-MCP to use user-specific endpoints")
    print("   ✅ Changed from /api/winplus/clients to /api/winplus/clients/code/{username}")
    print("   ✅ Changed sales to use client-specific endpoint")
    print("   ✅ Updated data formatting for single client object")
    print("   ✅ Tested end-to-end user-specific data flow")
