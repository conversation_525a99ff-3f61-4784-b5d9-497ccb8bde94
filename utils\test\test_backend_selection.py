#!/usr/bin/env python3
"""
Test the backend selection feature
"""

import requests
import json
import time

def test_win_mcp_backend_selection():
    """Test Win-MCP backend selection"""
    print("🏥 TESTING WIN-MCP BACKEND SELECTION")
    print("=" * 60)
    
    questions = [
        ("quel est mon groupe sanguin?", "Should get blood type from Win-MCP medical profile"),
        ("qui est mon médecin?", "Should get doctor info from Win-MCP"),
        ("quelles sont mes allergies?", "Should get allergies from Win-MCP")
    ]
    
    for i, (question, expected) in enumerate(questions, 1):
        print(f"\n🧪 Test {i}: {question}")
        print(f"   Expected: {expected}")
        
        conversation_id = f"test-win-mcp-{i}-{int(time.time())}"
        username = "user1"
        backend = "win-mcp"  # Explicitly specify Win-MCP
        
        try:
            # Step 1: Send the question with backend selection
            response1 = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": conversation_id,
                    "content": question,
                    "username": username,
                    "backend": backend
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Step 1 Status: {response1.status_code}")
            if response1.status_code == 200:
                data1 = response1.json()
                content1 = data1.get('content', '')
                print(f"   Step 1 Response: {content1[:100]}...")
                
                # Step 2: Select database option if prompted
                if "Souhaitez-vous que je vous réponde" in content1:
                    print(f"   🔄 Step 2: Selecting database option...")
                    
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": conversation_id,  # SAME ID
                            "content": "1",
                            "username": username,
                            "backend": backend  # SAME BACKEND
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    print(f"   Step 2 Status: {response2.status_code}")
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   Step 2 Response: {content2[:200]}...")
                        
                        # Analyze the response
                        if "je n'ai pas trouvé" in content2.lower():
                            print(f"   ❌ FAILED: Still getting 'info not found'")
                        elif any(keyword in content2.lower() for keyword in ['o+', 'dr.', 'hassan', 'alami', 'pénicilline', 'aspirine']):
                            print(f"   🎉 SUCCESS: Got Win-MCP medical data!")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                    else:
                        print(f"   ❌ Step 2 failed: {response2.text}")
                else:
                    print(f"   ⚠️  No source selection prompt")
                    
            else:
                print(f"   ❌ Step 1 failed: {response1.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_chat_mcp_backend_selection():
    """Test Chat-MCP backend selection"""
    print(f"\n💰 TESTING CHAT-MCP BACKEND SELECTION")
    print("=" * 60)
    
    questions = [
        ("my email?", "Should get email from Chat-MCP user data"),
        ("my name?", "Should get name from Chat-MCP user data"),
        ("my transactions?", "Should get transactions from Chat-MCP")
    ]
    
    for i, (question, expected) in enumerate(questions, 1):
        print(f"\n🧪 Test {i}: {question}")
        print(f"   Expected: {expected}")
        
        conversation_id = f"test-chat-mcp-{i}-{int(time.time())}"
        username = "user1"
        backend = "chat-mcp"  # Explicitly specify Chat-MCP
        
        try:
            # Step 1: Send the question with backend selection
            response1 = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": conversation_id,
                    "content": question,
                    "username": username,
                    "backend": backend
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Step 1 Status: {response1.status_code}")
            if response1.status_code == 200:
                data1 = response1.json()
                content1 = data1.get('content', '')
                print(f"   Step 1 Response: {content1[:100]}...")
                
                # Step 2: Select database option if prompted
                if "Souhaitez-vous que je vous réponde" in content1:
                    print(f"   🔄 Step 2: Selecting database option...")
                    
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": conversation_id,  # SAME ID
                            "content": "1",
                            "username": username,
                            "backend": backend  # SAME BACKEND
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    print(f"   Step 2 Status: {response2.status_code}")
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   Step 2 Response: {content2[:200]}...")
                        
                        # Check for Chat-MCP data
                        if "je n'ai pas trouvé" in content2.lower():
                            print(f"   ❌ FAILED: Still getting 'info not found'")
                        elif any(keyword in content2.lower() for keyword in ['<EMAIL>', 'test user1', 'transaction']):
                            print(f"   🎉 SUCCESS: Got Chat-MCP data!")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                    else:
                        print(f"   ❌ Step 2 failed: {response2.text}")
                else:
                    print(f"   ⚠️  No source selection prompt")
                    
            else:
                print(f"   ❌ Step 1 failed: {response1.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    print("🔄 TESTING BACKEND SELECTION FEATURE")
    print("=" * 70)
    print("Testing the new backend selection feature...")
    
    # Test Win-MCP backend selection
    test_win_mcp_backend_selection()
    
    # Test Chat-MCP backend selection
    test_chat_mcp_backend_selection()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 BACKEND SELECTION TEST SUMMARY")
    print(f"=" * 70)
    print(f"✅ Applied fixes:")
    print(f"   - Added 'backend' field to ChatRequest")
    print(f"   - Updated AiChatService to use request backend instead of config")
    print(f"   - Updated Angular MCP service to send backend selection")
    print(f"   - Updated AuthService to save selected backend to localStorage")
    print(f"\n💡 EXPECTED RESULTS:")
    print(f"   - Win-MCP questions should go to Win-MCP backend")
    print(f"   - Chat-MCP questions should go to Chat-MCP backend")
    print(f"   - Each backend should return appropriate data")
    print(f"\n🔍 CHECK MCP MICROSERVICE LOGS:")
    print(f"   Look for new debug messages:")
    print(f"   - '🔄 Using backend from request: win-mcp'")
    print(f"   - '🔄 Using backend from request: chat-mcp'")
    print(f"   - Different authentication flows for each backend")

if __name__ == "__main__":
    main()
