package com.example.mcp_microservice_chatboot_ai.controller;

import com.example.mcp_microservice_chatboot_ai.model.dto.ChatRequest;
import com.example.mcp_microservice_chatboot_ai.model.dto.McpRequest;
import com.example.mcp_microservice_chatboot_ai.service.AiChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * Controller for handling MCP (Model Context Protocol) endpoints.
 * Provides smart AI-powered endpoints for different backend systems.
 */
@RestController
@RequestMapping("/api/mcp")
public class McpController {

    private final AiChatService aiChatService;

    @Autowired
    public McpController(AiChatService aiChatService) {
        this.aiChatService = aiChatService;
    }

    /**
     * Smart Win-MCP endpoint for WinPlus pharmacy system questions.
     * Intelligently processes any question about WinPlus data including:
     * - Clients, Products, Sales, Purchases
     * - Medical profiles, Suppliers (NEW!)
     * - Dashboard statistics
     * 
     * @param chatRequest The chat request containing username and question
     * @return A Mono containing the AI response with WinPlus data
     */
    @PostMapping(value = "/smart-winmcp", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<String> smartWinMcp(@RequestBody McpRequest mcpRequest) {
        System.out.println("🧠 SMART WIN-MCP: Received question: " + mcpRequest.getQuestion());
        ChatRequest chatRequest = mcpRequest.toChatRequest();
        return aiChatService.processChat(chatRequest)
                .map(response -> response.getContent())
                .doOnSuccess(result -> System.out.println("✅ SMART WIN-MCP: Response generated successfully"))
                .doOnError(error -> System.out.println("❌ SMART WIN-MCP: Error - " + error.getMessage()));
    }

    /**
     * Smart Chat-MCP endpoint for Chat-MCP system questions.
     * Processes questions about chat-mcp data including:
     * - User conversations, messages
     * - User profiles and data
     * 
     * @param chatRequest The chat request containing username and question
     * @return A Mono containing the AI response with Chat-MCP data
     */
    @PostMapping(value = "/smart-chatmcp", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<String> smartChatMcp(@RequestBody McpRequest mcpRequest) {
        System.out.println("🧠 SMART CHAT-MCP: Received question: " + mcpRequest.getQuestion());
        ChatRequest chatRequest = mcpRequest.toChatRequest();
        return aiChatService.processChat(chatRequest)
                .map(response -> response.getContent())
                .doOnSuccess(result -> System.out.println("✅ SMART CHAT-MCP: Response generated successfully"))
                .doOnError(error -> System.out.println("❌ SMART CHAT-MCP: Error - " + error.getMessage()));
    }

    /**
     * General MCP chat endpoint (legacy support).
     * Routes to the appropriate backend based on configuration.
     * 
     * @param chatRequest The chat request
     * @return A Mono containing the AI response
     */
    @PostMapping(value = "/chat", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<String> chat(@RequestBody ChatRequest chatRequest) {
        System.out.println("🔄 MCP CHAT: Received message: " + chatRequest.getContent());
        return aiChatService.processChat(chatRequest)
                .map(response -> response.getContent())
                .doOnSuccess(result -> System.out.println("✅ MCP CHAT: Response generated successfully"))
                .doOnError(error -> System.out.println("❌ MCP CHAT: Error - " + error.getMessage()));
    }
}
