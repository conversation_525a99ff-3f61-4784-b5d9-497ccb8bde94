package com.chatbootmcp.chatmcp.repository;

import com.chatbootmcp.chatmcp.entity.Fournisseur;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Fournisseur entity
 */
@Repository
public interface FournisseurRepository extends JpaRepository<Fournisseur, Long> {

    Optional<Fournisseur> findByCodeFournisseur(String codeFournisseur);

    Page<Fournisseur> findByRaisonSocialeContainingIgnoreCase(String raisonSociale, Pageable pageable);

    Page<Fournisseur> findByEstActifTrue(Pageable pageable);

    List<Fournisseur> findByEstActifTrue();

    Page<Fournisseur> findByNomCommercialContainingIgnoreCase(String nomCommercial, Pageable pageable);

    Page<Fournisseur> findByVilleIgnoreCase(String ville, Pageable pageable);

    Page<Fournisseur> findByEstLaboratoireTrue(Pageable pageable);

    @Query("SELECT f FROM Fournisseur f ORDER BY f.raisonSociale")
    List<Fournisseur> findAllOrderByRaisonSociale();

    @Query("SELECT f FROM Fournisseur f WHERE f.estActif = true ORDER BY f.soldeFournisseur DESC")
    Page<Fournisseur> findActiveOrderBySoldeDesc(Pageable pageable);

    @Query("SELECT COUNT(f) FROM Fournisseur f WHERE f.estActif = true")
    long countActiveFournisseurs();

    @Query("SELECT SUM(f.soldeFournisseur) FROM Fournisseur f WHERE f.estActif = true")
    Double getTotalSoldeActifs();
}
