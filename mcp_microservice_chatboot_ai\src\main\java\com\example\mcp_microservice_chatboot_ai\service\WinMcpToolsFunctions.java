package com.example.mcp_microservice_chatboot_ai.service;

import com.example.mcp_microservice_chatboot_ai.config.ApiUrlsConfig;
import com.example.mcp_microservice_chatboot_ai.model.UserInvoicesRequest;
import com.example.mcp_microservice_chatboot_ai.model.UserProfileRequest;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Service containing tool functions for communicating with win-mcp (WinPlus) backend
 * This service handles all interactions with the WinPlus simulation APIs
 */
@Service
public class WinMcpToolsFunctions {

    private final WinMcpAuthService winMcpAuthService; // For Win-MCP authentication
    private final ApiUrlsConfig apiUrlsConfig;
    private final WebClient winMcpWebClient;

    @Autowired
    public WinMcpToolsFunctions(WinMcpAuthService winMcpAuthService, ApiUrlsConfig apiUrlsConfig) {
        this.winMcpAuthService = winMcpAuthService;
        this.apiUrlsConfig = apiUrlsConfig;
        this.winMcpWebClient = WebClient.builder()
                .baseUrl(apiUrlsConfig.getWinMcpBaseUrl())
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    /**
     * 🧠 SMART MCP FUNCTION for Win-MCP (WinPlus)
     * This is the main intelligent function that handles ANY question about WinPlus data
     * It fetches ALL data types and lets AI process them intelligently
     */
    public Function<UserProfileRequest, String> getSmartWinMcpDataTool() {
        return request -> {
            try {
                System.out.println("🧠 SMART WIN-MCP: Processing question: " + request.username());

                // Authenticate with Win-MCP using proper dual authentication
                WinMcpAuthService.WinMcpAuthResult authResult = winMcpAuthService.authenticateUser(request.username())
                        .subscribeOn(Schedulers.boundedElastic())
                        .timeout(Duration.ofSeconds(10))
                        .block();

                if (authResult == null || authResult.getUserToken() == null) {
                    System.out.println("❌ SMART WIN-MCP: Authentication failed for user: " + request.username());
                    return "Erreur d'authentification pour l'utilisateur: " + request.username();
                }

                System.out.println("🔑 SMART WIN-MCP: Authentication successful (" +
                    (authResult.isDualAuth() ? "DUAL" : "SINGLE") + " mode), fetching comprehensive data...");

                // Create WebClient with authentication headers
                WebClient.Builder webClientBuilder = WebClient.builder()
                        .baseUrl(apiUrlsConfig.getWinMcpBaseUrl())
                        .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

                // Add authentication headers based on auth type
                Map<String, String> authHeaders = authResult.getHeaders();
                for (Map.Entry<String, String> header : authHeaders.entrySet()) {
                    webClientBuilder.defaultHeader(header.getKey(), header.getValue());
                }

                WebClient webClient = webClientBuilder.build();

                // Fetch ALL data types in parallel for maximum flexibility
                System.out.println("🔍 SMART WIN-MCP: Fetching data from all available endpoints...");

                // Try to get user data (but handle errors gracefully)
                Mono<Map> userDataMono = webClient.get()
                        .uri("/api/winplus/user-data/" + request.username())
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ User data fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ User data failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>());

                // Try to get USER-SPECIFIC client data (not all clients)
                Mono<Map> clientDataMono = webClient.get()
                        .uri("/api/winplus/clients/code/" + request.username())
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ User-specific client data fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ User-specific client data failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>());

                // Try to get sales statistics
                Mono<Map> salesDataMono = webClient.get()
                        .uri("/api/winplus/ventes/statistics")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ Sales data fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ Sales data failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>());

                // Try to get products data
                Mono<Map> productsDataMono = webClient.get()
                        .uri("/api/winplus/produits")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ Products data fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ Products data failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>());

                Mono<Map> dashboardDataMono = webClient.get()
                        .uri("/api/winplus/dashboard/summary")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ Dashboard data fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ Dashboard data failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>());

                // Get medical profile data (using the correct endpoint)
                Mono<Map> medicalProfileMono = webClient.get()
                        .uri("/api/winplus/medical-profile/" + request.username())
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ Medical profile fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ Medical profile failed: " + error.getMessage()))
                        .onErrorReturn(Map.of("status", "error", "message", "Medical profile not available"));

                // Try to get USER-SPECIFIC client sales data (invoices/ventes)
                // First get client data to extract client ID, then get sales for that specific client
                Mono<Map> clientSalesDataMono = clientDataMono
                        .flatMap(clientData -> {
                            if (clientData != null && clientData.containsKey("id")) {
                                Long clientId = Long.valueOf(clientData.get("id").toString());
                                return webClient.get()
                                        .uri("/api/winplus/ventes/client/" + clientId + "?size=10")
                                        .retrieve()
                                        .bodyToMono(Map.class)
                                        .doOnSuccess(data -> System.out.println("✅ User-specific sales data fetched successfully"))
                                        .doOnError(error -> System.out.println("⚠️ User-specific sales data failed: " + error.getMessage()));
                            } else {
                                System.out.println("⚠️ No client ID found, cannot fetch user-specific sales");
                                return Mono.just(new HashMap<>());
                            }
                        })
                        .onErrorReturn(new HashMap<>());

                // Try to get suppliers data (NEW DATA TYPE)
                Mono<Map> suppliersDataMono = webClient.get()
                        .uri("/api/winplus/fournisseurs?size=10")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .doOnSuccess(data -> System.out.println("✅ Suppliers data fetched successfully"))
                        .doOnError(error -> System.out.println("⚠️ Suppliers data failed: " + error.getMessage()))
                        .onErrorReturn(new HashMap<>());

                // Combine all data and let AI process it intelligently
                return Mono.zip(userDataMono, clientDataMono, salesDataMono, productsDataMono, dashboardDataMono, clientSalesDataMono, medicalProfileMono, suppliersDataMono)
                        .map(tuple -> {
                            Map<String, Object> userData = (Map<String, Object>) tuple.getT1();
                            Map<String, Object> clientData = (Map<String, Object>) tuple.getT2();
                            Map<String, Object> salesStats = (Map<String, Object>) tuple.getT3();
                            Map<String, Object> productsData = (Map<String, Object>) tuple.getT4();
                            Map<String, Object> dashboardData = (Map<String, Object>) tuple.getT5();
                            Map<String, Object> clientSalesData = (Map<String, Object>) tuple.getT6();
                            Map<String, Object> medicalProfileData = (Map<String, Object>) tuple.getT7();
                            Map<String, Object> suppliersData = (Map<String, Object>) tuple.getT8();

                            // Count fetched data
                            int clientSalesCount = 0;
                            int productsCount = 0;

                            if (clientSalesData.containsKey("ventes")) {
                                List<Map<String, Object>> sales = (List<Map<String, Object>>) clientSalesData.get("ventes");
                                clientSalesCount = sales != null ? sales.size() : 0;
                            }

                            if (productsData.containsKey("produits")) {
                                List<Map<String, Object>> products = (List<Map<String, Object>>) productsData.get("produits");
                                productsCount = products != null ? products.size() : 0;
                            }

                            System.out.println("📊 SMART WIN-MCP: Fetched " + clientSalesCount + " sales, " + productsCount + " products");

                            // Create comprehensive data structure for AI processing
                            return formatSmartWinMcpData(userData, clientData, salesStats, productsData, dashboardData, clientSalesData, medicalProfileData, suppliersData);
                        })
                        .subscribeOn(Schedulers.boundedElastic())
                        .timeout(Duration.ofSeconds(30))
                        .block();

            } catch (Exception e) {
                System.out.println("❌ SMART WIN-MCP Error: " + e.getMessage());
                e.printStackTrace();
                return "Erreur lors de la récupération des données WinPlus: " + e.getMessage();
            }
        };
    }













    // ==================== FORMATTING METHODS ====================













    /**
     * 🧠 SMART FORMATTING for Win-MCP Data
     * Creates a comprehensive, structured data format for AI processing
     */
    private String formatSmartWinMcpData(Map<String, Object> userData, Map<String, Object> clientData,
                                         Map<String, Object> salesStats, Map<String, Object> productsData,
                                         Map<String, Object> dashboardData, Map<String, Object> clientSalesData,
                                         Map<String, Object> medicalProfileData, Map<String, Object> suppliersData) {
        StringBuilder sb = new StringBuilder();

        sb.append("=== 🧠 SMART WIN-MCP DATA ANALYSIS ===\n\n");

        // User Profile Section
        if (userData != null && !userData.isEmpty()) {
            sb.append("👤 PROFIL UTILISATEUR:\n");
            if (userData.containsKey("user")) {
                Map<String, Object> user = (Map<String, Object>) userData.get("user");
                sb.append("  • Nom d'utilisateur: ").append(user.get("username")).append("\n");
                sb.append("  • Email: ").append(user.get("email")).append("\n");
                sb.append("  • Nom complet: ").append(user.get("fullName")).append("\n");
            }
            sb.append("\n");
        }

        // User-Specific Client Information (for questions like "my client info", "my account")
        if (clientData != null && !clientData.isEmpty() && clientData.containsKey("nom")) {
            // This is now a single client object, not a list
            sb.append("🏢 INFORMATIONS CLIENT PERSONNEL:\n");
            sb.append("  • Nom: ").append(clientData.get("nom")).append(" ").append(clientData.get("prenom"));
            sb.append(" (").append(clientData.get("codeClient")).append(")\n");
            sb.append("  • Email: ").append(clientData.get("email")).append("\n");
            sb.append("  • Téléphone: ").append(clientData.get("gsm")).append("\n");
            sb.append("  • Adresse: ").append(clientData.get("adr1"));
            if (clientData.get("adr2") != null) {
                sb.append(", ").append(clientData.get("adr2"));
            }
            sb.append("\n");
            sb.append("  • Solde actuel: ").append(clientData.get("soldeClient")).append(" DH\n");
            sb.append("  • Plafond crédit: ").append(clientData.get("plafondCredit")).append(" DH\n");
            sb.append("  • Chiffre d'affaires: ").append(clientData.get("caClient")).append(" DH\n");
            sb.append("  • Taux remise: ").append(clientData.get("tauxRemise")).append("%\n");
            sb.append("  • Statut: ").append(clientData.get("estActif").equals(true) ? "Actif" : "Inactif").append("\n\n");
        }

        // Sales/Ventes Database Section (for business questions like "my invoices", "sales data")
        if (clientSalesData != null && clientSalesData.containsKey("ventes")) {
            List<Map<String, Object>> ventes = (List<Map<String, Object>>) clientSalesData.get("ventes");
            int totalItems = clientSalesData.containsKey("totalItems") ? (Integer) clientSalesData.get("totalItems") : ventes.size();

            sb.append("💰 BASE DE DONNÉES VENTES (").append(totalItems).append(" ventes):\n");

            double totalMontant = 0.0;
            double totalEncaisse = 0.0;
            int ventesTerminees = 0;

            for (Map<String, Object> vente : ventes) {
                sb.append("  • Vente #").append(vente.get("numVente"));
                sb.append(" - Date: ").append(vente.get("dateVente"));
                sb.append(" - Client: ").append(vente.get("nomClient"));
                sb.append(" - Montant: ").append(vente.get("mntNetTtc")).append(" DH");
                sb.append(" - Statut: ").append(vente.get("statut"));
                sb.append(" - Encaissé: ").append(vente.get("mntEncaisse")).append(" DH\n");

                // Calculate totals
                if (vente.get("mntNetTtc") != null) {
                    totalMontant += Double.parseDouble(vente.get("mntNetTtc").toString());
                }
                if (vente.get("mntEncaisse") != null) {
                    totalEncaisse += Double.parseDouble(vente.get("mntEncaisse").toString());
                }
                if ("TERMINE".equals(vente.get("statut"))) {
                    ventesTerminees++;
                }
            }

            sb.append("  📊 TOTAUX VENTES:\n");
            sb.append("    - Montant total: ").append(String.format("%.2f", totalMontant)).append(" DH\n");
            sb.append("    - Total encaissé: ").append(String.format("%.2f", totalEncaisse)).append(" DH\n");
            sb.append("    - Ventes terminées: ").append(ventesTerminees).append("/").append(totalItems).append("\n");
            sb.append("    - Montant moyen par vente: ").append(String.format("%.2f", totalMontant / totalItems)).append(" DH\n\n");
        }

        // Medical Profile Section
        if (medicalProfileData != null && medicalProfileData.containsKey("medicalProfile")) {
            Map<String, Object> profile = (Map<String, Object>) medicalProfileData.get("medicalProfile");
            sb.append("🏥 PROFIL MÉDICAL PERSONNEL:\n");
            sb.append("  • Groupe sanguin: ").append(profile.get("bloodType")).append("\n");
            sb.append("  • Allergies: ").append(profile.get("allergies")).append("\n");
            sb.append("  • Conditions chroniques: ").append(profile.get("chronicConditions")).append("\n");
            sb.append("  • Médicaments actuels: ").append(profile.get("currentMedications")).append("\n");
            sb.append("  • Contact d'urgence: ").append(profile.get("emergencyContact")).append("\n");
            sb.append("  • Téléphone urgence: ").append(profile.get("emergencyPhone")).append("\n");
            sb.append("  • Numéro assurance: ").append(profile.get("insuranceNumber")).append("\n");
            sb.append("  • Médecin traitant: ").append(profile.get("doctorName")).append("\n");
            sb.append("  • Téléphone médecin: ").append(profile.get("doctorPhone")).append("\n");
            sb.append("  • Date de naissance: ").append(profile.get("birthDate")).append("\n");
            sb.append("  • Taille: ").append(profile.get("heightCm")).append(" cm\n");
            sb.append("  • Poids: ").append(profile.get("weightKg")).append(" kg\n");
            sb.append("  • Dernière visite: ").append(profile.get("lastCheckupDate")).append("\n");
            sb.append("  • Notes médicales: ").append(profile.get("medicalNotes")).append("\n\n");
        }

        // Client Sales Section
        if (clientSalesData != null && clientSalesData.containsKey("ventes")) {
            List<Map<String, Object>> ventes = (List<Map<String, Object>>) clientSalesData.get("ventes");
            sb.append("💰 VENTES DU CLIENT (").append(ventes.size()).append(" ventes):\n");

            double totalSales = 0.0;
            double totalRemise = 0.0;
            int totalQuantity = 0;

            for (Map<String, Object> vente : ventes) {
                sb.append("  • Vente #").append(vente.get("numVente"));
                sb.append(" - Date: ").append(vente.get("dateVente"));
                sb.append(" - Montant: ").append(vente.get("mntNetTtc")).append(" DH");
                sb.append(" - Qté: ").append(vente.get("totalQte"));
                sb.append(" - Remise: ").append(vente.get("mntRemiseTtc")).append(" DH\n");

                // Calculate totals
                if (vente.get("mntNetTtc") != null) {
                    totalSales += Double.parseDouble(vente.get("mntNetTtc").toString());
                }
                if (vente.get("mntRemiseTtc") != null) {
                    totalRemise += Double.parseDouble(vente.get("mntRemiseTtc").toString());
                }
                if (vente.get("totalQte") != null) {
                    // Handle both integer and decimal quantity values
                    String qteStr = vente.get("totalQte").toString();
                    totalQuantity += (int) Double.parseDouble(qteStr);
                }
            }

            sb.append("  📊 TOTAUX CALCULÉS:\n");
            sb.append("    - Total des ventes: ").append(String.format("%.2f", totalSales)).append(" DH\n");
            sb.append("    - Total des remises: ").append(String.format("%.2f", totalRemise)).append(" DH\n");
            sb.append("    - Quantité totale: ").append(totalQuantity).append("\n");
            sb.append("    - Vente moyenne: ").append(String.format("%.2f", totalSales / ventes.size())).append(" DH\n\n");
        }

        // Global Sales Statistics
        if (salesStats != null && !salesStats.isEmpty()) {
            sb.append("📈 STATISTIQUES GLOBALES DES VENTES:\n");
            if (salesStats.containsKey("totalSales")) {
                sb.append("  • Total des ventes: ").append(salesStats.get("totalSales")).append(" DH\n");
            }
            if (salesStats.containsKey("numberOfSales")) {
                sb.append("  • Nombre de ventes: ").append(salesStats.get("numberOfSales")).append("\n");
            }
            if (salesStats.containsKey("averageSale")) {
                sb.append("  • Vente moyenne: ").append(salesStats.get("averageSale")).append(" DH\n");
            }
            sb.append("\n");
        }

        // Products Section
        if (productsData != null && productsData.containsKey("produits")) {
            List<Map<String, Object>> produits = (List<Map<String, Object>>) productsData.get("produits");
            sb.append("🏥 PRODUITS DISPONIBLES (").append(produits.size()).append(" produits):\n");

            double totalValue = 0.0;
            int totalStock = 0;

            for (Map<String, Object> produit : produits) {
                sb.append("  • ").append(produit.get("designation"));
                sb.append(" (").append(produit.get("codePrd")).append(")");
                sb.append(" - Prix: ").append(produit.get("prixVenteStd")).append(" DH");
                sb.append(" - Stock: ").append(produit.get("totalStock"));
                sb.append(" - Prescription: ").append(produit.get("estOblgPrescription").equals(true) ? "Oui" : "Non").append("\n");

                // Calculate totals
                if (produit.get("prixVenteStd") != null) {
                    totalValue += Double.parseDouble(produit.get("prixVenteStd").toString());
                }
                if (produit.get("totalStock") != null) {
                    // Handle both integer and decimal stock values
                    String stockStr = produit.get("totalStock").toString();
                    totalStock += (int) Double.parseDouble(stockStr);
                }
            }

            sb.append("  📊 TOTAUX PRODUITS:\n");
            sb.append("    - Valeur totale des prix: ").append(String.format("%.2f", totalValue)).append(" DH\n");
            sb.append("    - Stock total: ").append(totalStock).append("\n");
            sb.append("    - Prix moyen: ").append(String.format("%.2f", totalValue / produits.size())).append(" DH\n\n");
        }

        // Suppliers Database Section (NEW DATA TYPE)
        if (suppliersData != null && suppliersData.containsKey("fournisseurs")) {
            List<Map<String, Object>> fournisseurs = (List<Map<String, Object>>) suppliersData.get("fournisseurs");
            int totalItems = suppliersData.containsKey("totalItems") ? (Integer) suppliersData.get("totalItems") : fournisseurs.size();

            sb.append("🏭 BASE DE DONNÉES FOURNISSEURS (").append(totalItems).append(" fournisseurs):\n");

            double totalSolde = 0.0;
            int activeFournisseurs = 0;
            int laboratoires = 0;

            for (Map<String, Object> fournisseur : fournisseurs) {
                sb.append("  • Fournisseur: ").append(fournisseur.get("raisonSociale"));
                sb.append(" (").append(fournisseur.get("codeFournisseur")).append(")");
                sb.append(" - Email: ").append(fournisseur.get("email"));
                sb.append(" - Tél: ").append(fournisseur.get("telephone"));
                sb.append(" - Ville: ").append(fournisseur.get("ville"));
                sb.append(" - Solde: ").append(fournisseur.get("soldeFournisseur")).append(" DH");
                sb.append(" - Statut: ").append(fournisseur.get("estActif").equals(true) ? "Actif" : "Inactif");
                sb.append(" - Laboratoire: ").append(fournisseur.get("estLaboratoire").equals(true) ? "Oui" : "Non").append("\n");

                // Calculate totals
                if (fournisseur.get("soldeFournisseur") != null) {
                    totalSolde += Double.parseDouble(fournisseur.get("soldeFournisseur").toString());
                }
                if (fournisseur.get("estActif") != null && fournisseur.get("estActif").equals(true)) {
                    activeFournisseurs++;
                }
                if (fournisseur.get("estLaboratoire") != null && fournisseur.get("estLaboratoire").equals(true)) {
                    laboratoires++;
                }
            }

            sb.append("  📊 TOTAUX FOURNISSEURS:\n");
            sb.append("    - Solde total: ").append(String.format("%.2f", totalSolde)).append(" DH\n");
            sb.append("    - Fournisseurs actifs: ").append(activeFournisseurs).append("/").append(totalItems).append("\n");
            sb.append("    - Laboratoires: ").append(laboratoires).append("/").append(totalItems).append("\n");
            sb.append("    - Solde moyen par fournisseur: ").append(String.format("%.2f", totalSolde / totalItems)).append(" DH\n\n");
        }

        // Dashboard Summary
        if (dashboardData != null && !dashboardData.isEmpty()) {
            sb.append("📊 TABLEAU DE BORD:\n");
            if (dashboardData.containsKey("clients")) {
                Map<String, Object> clients = (Map<String, Object>) dashboardData.get("clients");
                sb.append("  • Clients total: ").append(clients.get("total")).append("\n");
                sb.append("  • Clients actifs: ").append(clients.get("active")).append("\n");
            }
            if (dashboardData.containsKey("products")) {
                Map<String, Object> products = (Map<String, Object>) dashboardData.get("products");
                sb.append("  • Produits total: ").append(products.get("total")).append("\n");
                sb.append("  • Produits vendables: ").append(products.get("vendable")).append("\n");
            }
            if (dashboardData.containsKey("sales")) {
                Map<String, Object> sales = (Map<String, Object>) dashboardData.get("sales");
                sb.append("  • Ventes (30 derniers jours): ").append(sales.get("last30Days")).append(" DH\n");
            }
            if (dashboardData.containsKey("purchases")) {
                Map<String, Object> purchases = (Map<String, Object>) dashboardData.get("purchases");
                sb.append("  • Achats (30 derniers jours): ").append(purchases.get("last30Days")).append(" DH\n");
                sb.append("  • Montant impayé: ").append(purchases.get("outstanding")).append(" DH\n");
            }
            sb.append("\n");
        }

        sb.append("=== FIN DES DONNÉES WIN-MCP ===\n");
        sb.append("Cette structure contient toutes les informations disponibles pour répondre à toute question sur les données WinPlus.");

        return sb.toString();
    }
}
