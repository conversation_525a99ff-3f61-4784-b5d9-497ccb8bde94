#!/usr/bin/env python3
"""
Test script for dual authentication system
Tests both tenant and user authentication for Win-MCP
"""

import requests
import json

def test_tenant_authentication():
    """Test tenant authentication endpoint"""
    print("🏥 Testing tenant authentication...")
    
    url = "http://localhost:8082/auth/tenant/login"
    payload = {
        "username": "0001",
        "password": "123456"
    }
    
    try:
        response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            tenant_token = data.get('token')
            print(f"✅ Tenant authentication successful! Token: {tenant_token[:20]}...")
            return tenant_token
        else:
            print(f"❌ Tenant authentication failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error during tenant authentication: {e}")
        return None

def test_user_authentication_with_tenant(tenant_token):
    """Test user authentication with tenant token"""
    print("\n👤 Testing user authentication with tenant token...")
    
    url = "http://localhost:8082/auth/login"
    payload = {
        "username": "user1",
        "password": "password"
    }
    
    headers = {
        'Content-Type': 'application/json',
        'AuthorizationTenant': f'BearerTenant {tenant_token}'
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            user_token = data.get('token')
            print(f"✅ User authentication successful! Token: {user_token[:20]}...")
            return user_token
        else:
            print(f"❌ User authentication failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error during user authentication: {e}")
        return None

def test_api_with_dual_auth(tenant_token, user_token):
    """Test API call with dual authentication"""
    print("\n🔐 Testing API call with dual authentication...")
    
    url = "http://localhost:8082/api/winplus/medical-profile/user1"
    
    headers = {
        'Content-Type': 'application/json',
        'AuthorizationTenant': f'BearerTenant {tenant_token}',
        'Authorization': f'Bearer {user_token}'
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Dual authentication API call successful!")
            return True
        else:
            print(f"❌ Dual authentication API call failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error during dual authentication API call: {e}")
        return False

def test_mcp_microservice_with_dual_auth():
    """Test MCP microservice with dual authentication"""
    print("\n🧠 Testing MCP microservice with dual authentication...")
    
    url = "http://localhost:8081/api/chat"
    payload = {
        "conversationId": "test-dual-auth",
        "content": "quelles sont mes informations médicales personnelles?",
        "username": "user1"
    }
    
    try:
        response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ MCP microservice test successful!")
            return True
        else:
            print(f"❌ MCP microservice test failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error during MCP microservice test: {e}")
        return False

def main():
    print("🔐 Testing Dual Authentication System for Win-MCP")
    print("=" * 60)
    
    # Step 1: Test tenant authentication
    tenant_token = test_tenant_authentication()
    if not tenant_token:
        print("❌ Cannot proceed without tenant token")
        return
    
    # Step 2: Test user authentication with tenant token
    user_token = test_user_authentication_with_tenant(tenant_token)
    if not user_token:
        print("❌ Cannot proceed without user token")
        return
    
    # Step 3: Test API call with dual authentication
    api_success = test_api_with_dual_auth(tenant_token, user_token)
    
    # Step 4: Test MCP microservice
    mcp_success = test_mcp_microservice_with_dual_auth()
    
    print("\n" + "=" * 60)
    print("🎯 DUAL AUTHENTICATION TEST SUMMARY:")
    print(f"   🏥 Tenant Auth: {'✅ PASS' if tenant_token else '❌ FAIL'}")
    print(f"   👤 User Auth: {'✅ PASS' if user_token else '❌ FAIL'}")
    print(f"   🔐 Dual API: {'✅ PASS' if api_success else '❌ FAIL'}")
    print(f"   🧠 MCP Service: {'✅ PASS' if mcp_success else '❌ FAIL'}")
    
    if tenant_token and user_token and api_success and mcp_success:
        print("\n🎉 ALL TESTS PASSED! Dual authentication system is working correctly!")
    else:
        print("\n⚠️  Some tests failed. Please check the logs above.")

if __name__ == "__main__":
    main()
