#!/usr/bin/env python3
"""
Debug test for MCP microservice to trigger logs
"""

import requests
import json
import time

def test_mcp_debug():
    """Test MCP microservice and trigger logs"""
    
    url = "http://localhost:8081/api/mcp/smart-winmcp"
    payload = {
        "username": "testuser",
        "question": "my suppliers"
    }
    
    print("🔍 Making test call to trigger MCP logs...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        print("📞 Sending request...")
        response = requests.post(url, json=payload, timeout=30)
        print(f"📊 Status: {response.status_code}")
        print(f"📝 Response: {response.text}")
        
        print("\n⏰ Waiting 2 seconds for logs to appear...")
        time.sleep(2)
        
        print("✅ Request completed. Check MCP microservice logs for details.")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_mcp_debug()
