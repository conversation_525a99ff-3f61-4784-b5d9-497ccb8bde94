#!/usr/bin/env python3
"""
Test user1 medical profile specifically to debug the issue
"""

import requests
import json

def test_user1_medical_profile():
    """Test user1 medical profile with detailed debugging"""
    print("🩺 Testing user1 Medical Profile - Detailed Debug")
    print("=" * 50)
    
    # Step 1: Get tenant token
    print("Step 1: Getting tenant token...")
    tenant_url = "http://localhost:8082/auth/tenant/login"
    tenant_payload = {
        "username": "0001",
        "password": "123456"
    }
    
    try:
        tenant_response = requests.post(tenant_url, json=tenant_payload, headers={'Content-Type': 'application/json'})
        print(f"Tenant Status: {tenant_response.status_code}")
        
        if tenant_response.status_code != 200:
            print(f"❌ Tenant auth failed: {tenant_response.text}")
            return
        
        tenant_data = tenant_response.json()
        tenant_token = tenant_data.get('token')
        print(f"✅ Tenant token: {tenant_token[:20]}...")
        
    except Exception as e:
        print(f"❌ Tenant auth error: {e}")
        return
    
    # Step 2: Get user token
    print("\nStep 2: Getting user1 token...")
    user_url = "http://localhost:8082/auth/login"
    user_payload = {
        "username": "user1",
        "password": "password"
    }
    user_headers = {
        'Content-Type': 'application/json',
        'AuthorizationTenant': f'BearerTenant {tenant_token}'
    }
    
    try:
        user_response = requests.post(user_url, json=user_payload, headers=user_headers)
        print(f"User Status: {user_response.status_code}")
        
        if user_response.status_code != 200:
            print(f"❌ User auth failed: {user_response.text}")
            return
        
        user_data = user_response.json()
        user_token = user_data.get('token')
        print(f"✅ User token: {user_token[:20]}...")
        
    except Exception as e:
        print(f"❌ User auth error: {e}")
        return
    
    # Step 3: Test medical profile API
    print("\nStep 3: Testing medical profile API...")
    medical_url = f"http://localhost:8082/api/winplus/user-data/user1"
    medical_headers = {
        'AuthorizationTenant': f'BearerTenant {tenant_token}',
        'Authorization': f'Bearer {user_token}'
    }
    
    print(f"URL: {medical_url}")
    print(f"Headers: {medical_headers}")
    
    try:
        medical_response = requests.get(medical_url, headers=medical_headers)
        print(f"Medical API Status: {medical_response.status_code}")
        print(f"Response Headers: {dict(medical_response.headers)}")
        
        if medical_response.status_code == 200:
            try:
                medical_data = medical_response.json()
                print(f"✅ Medical data retrieved successfully!")
                print(f"Response keys: {list(medical_data.keys())}")
                
                if medical_data.get('medicalProfile'):
                    profile = medical_data['medicalProfile']
                    print(f"Medical Profile found:")
                    print(f"  - Blood Type: {profile.get('bloodType')}")
                    print(f"  - Allergies: {profile.get('allergies')}")
                    print(f"  - Doctor: {profile.get('doctorName')}")
                else:
                    print(f"⚠️  No medical profile in response")
                    print(f"Full response: {medical_data}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {medical_response.text[:200]}...")
                
        else:
            print(f"❌ Medical API failed: {medical_response.text}")
            
    except Exception as e:
        print(f"❌ Medical API error: {e}")

def test_simple_medical_api():
    """Test the medical API with a simpler approach"""
    print("\n🔬 Testing Simple Medical API Call")
    print("=" * 40)
    
    # Use the working authentication from our previous tests
    url = "http://localhost:8082/api/winplus/user-data/user1"
    
    # Get fresh tokens
    tenant_response = requests.post(
        "http://localhost:8082/auth/tenant/login",
        json={"username": "0001", "password": "123456"},
        headers={'Content-Type': 'application/json'}
    )
    
    if tenant_response.status_code != 200:
        print("❌ Cannot get tenant token")
        return
    
    tenant_token = tenant_response.json().get('token')
    
    user_response = requests.post(
        "http://localhost:8082/auth/login",
        json={"username": "user1", "password": "password"},
        headers={
            'Content-Type': 'application/json',
            'AuthorizationTenant': f'BearerTenant {tenant_token}'
        }
    )
    
    if user_response.status_code != 200:
        print("❌ Cannot get user token")
        return
    
    user_token = user_response.json().get('token')
    
    # Try the medical API
    try:
        response = requests.get(
            url,
            headers={
                'AuthorizationTenant': f'BearerTenant {tenant_token}',
                'Authorization': f'Bearer {user_token}'
            },
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Keys: {list(data.keys())}")
            if 'medicalProfile' in data:
                print(f"✅ Medical profile found!")
            else:
                print(f"⚠️  No medical profile")
        else:
            print(f"❌ Failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    test_user1_medical_profile()
    test_simple_medical_api()

if __name__ == "__main__":
    main()
