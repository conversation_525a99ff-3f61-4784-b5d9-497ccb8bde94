#!/usr/bin/env python3
"""
Test all the fixes applied:
1. AI Response improvements
2. Authentication fixes
3. Data retrieval
"""

import requests
import json

def test_win_mcp_medical_questions():
    """Test Win-MCP medical questions with improved AI"""
    print("🩺 TESTING WIN-MCP MEDICAL QUESTIONS (IMPROVED AI)")
    print("=" * 60)
    
    questions = [
        "quel est mon groupe sanguin?",
        "qui est mon médecin?",
        "quelles sont mes allergies?",
        "quelles sont mes informations médicales?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n🔍 Test {i}: {question}")
        
        try:
            # Send the question
            response = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": f"test-improved-{i}",
                    "content": question,
                    "username": "user1"
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                content = data.get('content', '')
                print(f"   💬 Response: {content[:100]}...")
                
                # If it asks for source selection, choose option 1 (database)
                if "Souhaitez-vous que je vous réponde" in content:
                    print(f"   🔄 Selecting database option...")
                    
                    # Send option 1
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": f"test-improved-{i}",
                            "content": "1",
                            "username": "user1"
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   ✅ Final Response: {content2[:200]}...")
                        
                        # Check if we got actual medical data
                        if any(keyword in content2.lower() for keyword in ['o+', 'dr.', 'hassan', 'alami', 'groupe sanguin', 'médecin', 'pénicilline', 'aspirine']):
                            print(f"   🎉 SUCCESS: Got real medical data!")
                        elif "je n'ai pas trouvé" in content2.lower() or "information n'est pas disponible" in content2.lower():
                            print(f"   ❌ FAILED: Still getting 'info not found'")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                    else:
                        print(f"   ❌ Option selection failed: {response2.text}")
                        
            else:
                print(f"   ❌ Failed: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_chat_mcp_questions():
    """Test Chat-MCP questions with improved AI"""
    print(f"\n💰 TESTING CHAT-MCP QUESTIONS (IMPROVED AI)")
    print("=" * 50)
    
    questions = [
        "quel est mon email?",
        "quelles sont mes dernières transactions?",
        "quelles sont mes factures?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n🔍 Test {i}: {question}")
        
        try:
            # Send the question
            response = requests.post(
                "http://localhost:8081/api/chat",
                json={
                    "conversationId": f"test-chat-improved-{i}",
                    "content": question,
                    "username": "user1"
                },
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                content = data.get('content', '')
                
                # If it asks for source selection, choose option 1 (database)
                if "Souhaitez-vous que je vous réponde" in content:
                    print(f"   🔄 Selecting database option...")
                    
                    # Send option 1
                    response2 = requests.post(
                        "http://localhost:8081/api/chat",
                        json={
                            "conversationId": f"test-chat-improved-{i}",
                            "content": "1",
                            "username": "user1"
                        },
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        content2 = data2.get('content', '')
                        print(f"   💬 Response: {content2[:200]}...")
                        
                        # Check for Chat-MCP data
                        if any(keyword in content2.lower() for keyword in ['email', 'transaction', 'facture', 'john.doe', 'user1']):
                            print(f"   🎉 SUCCESS: Got Chat-MCP data!")
                        elif "je n'ai pas trouvé" in content2.lower() or "information n'est pas disponible" in content2.lower():
                            print(f"   ❌ FAILED: Still getting 'info not found'")
                        else:
                            print(f"   ⚠️  UNCLEAR: Response unclear")
                else:
                    print(f"   💬 Direct Response: {content[:200]}...")
                        
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_authentication_flow():
    """Test authentication flows"""
    print(f"\n🔐 TESTING AUTHENTICATION FLOWS")
    print("=" * 40)
    
    # Test Chat-MCP authentication
    print("\n1. Testing Chat-MCP Authentication:")
    try:
        response = requests.post(
            "http://localhost:8080/api/auth/login",
            json={"username": "user1", "password": "password"},
            headers={'Content-Type': 'application/json'}
        )
        print(f"   Chat-MCP Auth: {response.status_code} {'✅ SUCCESS' if response.status_code == 200 else '❌ FAILED'}")
    except Exception as e:
        print(f"   Chat-MCP Auth: ❌ ERROR - {e}")
    
    # Test Win-MCP dual authentication
    print("\n2. Testing Win-MCP Dual Authentication:")
    try:
        # Step 1: Tenant auth
        tenant_response = requests.post(
            "http://localhost:8082/auth/tenant/login",
            json={"username": "0001", "password": "123456"},
            headers={'Content-Type': 'application/json'}
        )
        print(f"   Win-MCP Tenant: {tenant_response.status_code} {'✅ SUCCESS' if tenant_response.status_code == 200 else '❌ FAILED'}")
        
        if tenant_response.status_code == 200:
            tenant_token = tenant_response.json().get('token')
            
            # Step 2: User auth
            user_response = requests.post(
                "http://localhost:8082/auth/login",
                json={"username": "user1", "password": "password"},
                headers={
                    'Content-Type': 'application/json',
                    'AuthorizationTenant': f'BearerTenant {tenant_token}'
                }
            )
            print(f"   Win-MCP User: {user_response.status_code} {'✅ SUCCESS' if user_response.status_code == 200 else '❌ FAILED'}")
    except Exception as e:
        print(f"   Win-MCP Auth: ❌ ERROR - {e}")

def main():
    print("🔧 TESTING ALL FIXES")
    print("=" * 70)
    print("Testing:")
    print("1. ✅ Logout Button (added to Angular)")
    print("2. 🔧 AI Response improvements")
    print("3. 🔧 Win-MCP authentication guidance")
    print("4. 🔧 Data retrieval and processing")
    
    # Test authentication flows
    test_authentication_flow()
    
    # Test Win-MCP with improved AI
    test_win_mcp_medical_questions()
    
    # Test Chat-MCP with improved AI
    test_chat_mcp_questions()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 SUMMARY OF FIXES")
    print(f"=" * 70)
    print(f"✅ 1. Logout Button: Added to main app header")
    print(f"🔧 2. AI Response: Improved prompt to be more helpful")
    print(f"🔧 3. Win-MCP Auth: Added validation and guidance")
    print(f"🔧 4. Data Processing: Fixed AI interpretation")
    print(f"\n🎉 All major issues should now be resolved!")
    print(f"\n📋 NEXT STEPS:")
    print(f"1. Test logout button in Angular frontend")
    print(f"2. Try Win-MCP login with user1/password (not 0001/123456)")
    print(f"3. Ask medical questions and check for real data")
    print(f"4. Try Chat-MCP login and ask about email/transactions")

if __name__ == "__main__":
    main()
