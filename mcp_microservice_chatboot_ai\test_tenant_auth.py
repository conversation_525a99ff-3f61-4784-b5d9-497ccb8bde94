#!/usr/bin/env python3
"""
Test tenant authentication with correct credentials
"""

import requests
import json

def test_tenant_auth():
    """Test tenant authentication with correct password"""
    print("🏥 Testing tenant authentication with correct password...")
    
    url = "http://localhost:8082/auth/tenant/login"
    payload = {
        "username": "0001",
        "password": "password"  # Correct password
    }
    
    try:
        response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            print(f"✅ Tenant authentication successful! Token: {token[:20]}...")
            return token
        else:
            print(f"❌ Tenant authentication failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error during tenant authentication: {e}")
        return None

def test_tenant_auth_wrong_password():
    """Test tenant authentication with wrong password"""
    print("\n🏥 Testing tenant authentication with wrong password...")
    
    url = "http://localhost:8082/auth/tenant/login"
    payload = {
        "username": "0001",
        "password": "123456"  # Wrong password
    }
    
    try:
        response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("❌ This should have failed!")
            return True
        else:
            print(f"✅ Correctly failed with wrong password: {response.text}")
            return False
    except Exception as e:
        print(f"✅ Correctly failed with exception: {e}")
        return False

def main():
    print("🔐 Testing Tenant Authentication Credentials")
    print("=" * 50)
    
    # Test with correct password
    correct_result = test_tenant_auth()
    
    # Test with wrong password
    wrong_result = test_tenant_auth_wrong_password()
    
    print("\n" + "=" * 50)
    print("🎯 TENANT AUTHENTICATION TEST SUMMARY:")
    print(f"   ✅ Correct password: {'PASS' if correct_result else 'FAIL'}")
    print(f"   ❌ Wrong password: {'FAIL (as expected)' if not wrong_result else 'PASS (unexpected)'}")
    
    if correct_result and not wrong_result:
        print("\n🎉 Tenant authentication is working correctly!")
        print("The issue is that MCP microservice is using the wrong password.")
        print("MCP microservice should use 'password' not '123456'")
    else:
        print("\n⚠️  There's an issue with tenant authentication setup.")

if __name__ == "__main__":
    main()
