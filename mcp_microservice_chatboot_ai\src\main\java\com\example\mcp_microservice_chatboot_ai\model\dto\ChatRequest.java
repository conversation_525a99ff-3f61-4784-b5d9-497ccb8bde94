package com.example.mcp_microservice_chatboot_ai.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for chat message requests.
 */

/**
 * Role: Data Transfer Object for chat requests
    Purpose:
    Carries message content, conversation ID, and user information
    Used when sending messages to the AI service
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatRequest {
    
    /**
     * The ID of the conversation this message belongs to.
     */
    private String conversationId;
    
    /**
     * The content of the message.
     */
    private String content;
    
    /**
     * The username of the user sending the message.
     */
    private String username;

    /**
     * The backend to use for this request (chat-mcp or win-mcp).
     * If not specified, uses the configured default backend.
     */
    private String backend;

    /**
     * The knowledge mode for this request (general or internal).
     * - "general": Use general AI knowledge
     * - "internal": Use database/internal data
     * If not specified, defaults to "internal".
     */
    private String knowledgeMode;
}
