#!/usr/bin/env python3
"""
Test Win-MCP endpoints directly to see the actual data structure
"""

import requests
import json

def test_win_mcp_endpoints():
    """Test Win-MCP endpoints directly"""
    print("🔍 TESTING WIN-MCP ENDPOINTS DIRECTLY")
    print("=" * 60)
    
    # First authenticate to get tokens
    print("🔐 Step 1: Authenticating with Win-MCP...")
    
    # Tenant authentication
    tenant_auth = requests.post(
        "http://localhost:8082/auth/tenant/login",
        json={
            "username": "0001",
            "password": "123456"
        },
        headers={'Content-Type': 'application/json'}
    )
    
    if tenant_auth.status_code != 200:
        print(f"❌ Tenant auth failed: {tenant_auth.text}")
        return
    
    tenant_token = tenant_auth.json().get('token')
    print(f"✅ Tenant token: {tenant_token[:20]}...")
    
    # User authentication
    user_auth = requests.post(
        "http://localhost:8082/auth/login",
        json={
            "username": "user1",
            "password": "password"
        },
        headers={
            'Content-Type': 'application/json',
            'AuthorizationTenant': f'BearerTenant {tenant_token}'
        }
    )
    
    if user_auth.status_code != 200:
        print(f"❌ User auth failed: {user_auth.text}")
        return
    
    user_token = user_auth.json().get('token')
    print(f"✅ User token: {user_token[:20]}...")
    
    # Headers for API calls
    headers = {
        'Content-Type': 'application/json',
        'AuthorizationTenant': f'BearerTenant {tenant_token}',
        'Authorization': f'Bearer {user_token}'
    }
    
    print(f"\n🔍 Step 2: Testing endpoints...")
    
    endpoints = [
        ("/api/winplus/clients", "Clients data"),
        ("/api/winplus/ventes", "Sales/invoices data"),
        ("/api/winplus/produits", "Products data"),
        ("/api/winplus/dashboard/summary", "Dashboard data"),
        ("/api/winplus/ventes/statistics", "Sales statistics"),
        ("/api/winplus/user-data/user1", "User data"),
        ("/api/winplus/medical-profile/user1", "Medical profile")
    ]
    
    for endpoint, description in endpoints:
        print(f"\n📡 Testing: {endpoint} ({description})")
        
        try:
            response = requests.get(
                f"http://localhost:8082{endpoint}",
                headers=headers
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   Data type: {type(data)}")
                    
                    if isinstance(data, dict):
                        print(f"   Keys: {list(data.keys())}")
                        for key, value in data.items():
                            if isinstance(value, list):
                                print(f"     {key}: list with {len(value)} items")
                                if len(value) > 0:
                                    print(f"       First item type: {type(value[0])}")
                                    if isinstance(value[0], dict):
                                        print(f"       First item keys: {list(value[0].keys())}")
                            elif isinstance(value, dict):
                                print(f"     {key}: dict with keys {list(value.keys())}")
                            else:
                                print(f"     {key}: {type(value)} = {value}")
                    elif isinstance(data, list):
                        print(f"   List with {len(data)} items")
                        if len(data) > 0:
                            print(f"   First item type: {type(data[0])}")
                            if isinstance(data[0], dict):
                                print(f"   First item keys: {list(data[0].keys())}")
                    else:
                        print(f"   Data: {data}")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON decode error: {e}")
                    print(f"   Raw response: {response.text[:200]}...")
            else:
                print(f"   ❌ Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")

def main():
    print("🔍 WIN-MCP ENDPOINTS DIRECT TEST")
    print("=" * 70)
    print("Testing Win-MCP endpoints directly to understand data structure...")
    
    test_win_mcp_endpoints()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 ENDPOINT TEST SUMMARY")
    print(f"=" * 70)
    print(f"This test helps us understand:")
    print(f"✅ Which endpoints work")
    print(f"✅ What data structure each endpoint returns")
    print(f"✅ Why the MCP microservice parsing might be failing")
    print(f"\n💡 NEXT STEPS:")
    print(f"   - Fix data structure mismatches in WinMcpToolsFunctions")
    print(f"   - Update parsing logic to match actual endpoint responses")
    print(f"   - Ensure proper error handling for different data formats")

if __name__ == "__main__":
    main()
