#!/usr/bin/env python3
"""
Test the improved Win-MCP system with complete user-specific data
This test verifies that user<PERSON> now has client data and all users get proper responses
"""

import requests
import json
import time

def test_user_comprehensive_data(username):
    """Test comprehensive data for a given username"""
    
    print(f"\n🔍 Testing comprehensive data for: {username}")
    print("-" * 50)
    
    url = "http://localhost:8081/api/chat"
    conversation_id = f"test-{username}-{int(time.time())}"
    
    # Test 1: Client information
    print("📋 Test 1: Client Information")
    payload1 = {
        "conversationId": conversation_id,
        "username": username,
        "content": "my client information?",
        "backend": "WIN_MCP"
    }
    
    try:
        response1 = requests.post(url, json=payload1, timeout=30)
        if response1.status_code == 200:
            response1_data = response1.json()
            response1_text = response1_data.get('content', '')
            
            if "1️⃣" in response1_text:
                # Select option 1 (database)
                payload2 = {
                    "conversationId": conversation_id,
                    "username": username, 
                    "content": "1",
                    "backend": "WIN_MCP"
                }
                
                response2 = requests.post(url, json=payload2, timeout=60)
                if response2.status_code == 200:
                    response2_data = response2.json()
                    client_response = response2_data.get('content', '')
                    
                    print(f"   ✅ Client data received ({len(client_response)} chars)")
                    
                    # Check for specific client info
                    has_client_info = any(keyword in client_response.lower() for keyword in 
                                        ['nom', 'email', 'téléphone', 'adresse', 'solde'])
                    
                    if has_client_info:
                        print(f"   ✅ Contains client details")
                        # Extract key info
                        lines = client_response.split('\n')[:5]
                        for line in lines:
                            if line.strip():
                                print(f"      {line.strip()}")
                    else:
                        print(f"   ❌ No specific client information found")
                        print(f"      Response: {client_response[:200]}...")
                    
                    return {
                        'client_test': 'PASS' if has_client_info else 'FAIL',
                        'client_response': client_response[:300],
                        'client_length': len(client_response)
                    }
                    
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return {'client_test': 'ERROR', 'error': str(e)}
    
    return {'client_test': 'FAIL', 'error': 'No response'}

def test_user_sales_data(username):
    """Test sales data for a given username"""
    
    print(f"\n💰 Testing sales data for: {username}")
    print("-" * 30)
    
    url = "http://localhost:8081/api/chat"
    conversation_id = f"sales-{username}-{int(time.time())}"
    
    payload1 = {
        "conversationId": conversation_id,
        "username": username,
        "content": "my sales data?",
        "backend": "WIN_MCP"
    }
    
    try:
        response1 = requests.post(url, json=payload1, timeout=30)
        if response1.status_code == 200:
            response1_data = response1.json()
            response1_text = response1_data.get('content', '')
            
            if "1️⃣" in response1_text:
                payload2 = {
                    "conversationId": conversation_id,
                    "username": username,
                    "content": "1",
                    "backend": "WIN_MCP"
                }
                
                response2 = requests.post(url, json=payload2, timeout=60)
                if response2.status_code == 200:
                    response2_data = response2.json()
                    sales_response = response2_data.get('content', '')
                    
                    print(f"   ✅ Sales data received ({len(sales_response)} chars)")
                    
                    # Check for sales info
                    has_sales_info = any(keyword in sales_response.lower() for keyword in 
                                       ['vente', 'montant', 'total', 'dh', 'remise'])
                    
                    if has_sales_info:
                        print(f"   ✅ Contains sales details")
                        # Extract key numbers
                        lines = sales_response.split('\n')[:3]
                        for line in lines:
                            if line.strip() and ('total' in line.lower() or 'montant' in line.lower()):
                                print(f"      {line.strip()}")
                    else:
                        print(f"   ❌ No sales information found")
                    
                    return {
                        'sales_test': 'PASS' if has_sales_info else 'FAIL',
                        'sales_response': sales_response[:200],
                        'sales_length': len(sales_response)
                    }
                    
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return {'sales_test': 'ERROR', 'error': str(e)}
    
    return {'sales_test': 'FAIL', 'error': 'No response'}

def main():
    """Main test function"""
    
    print("🚀 TESTING IMPROVED WIN-MCP USER-SPECIFIC DATA")
    print("=" * 60)
    print("Testing that all users now have complete data:")
    print("- Client information")
    print("- Sales data")
    print("- User-specific responses")
    print("=" * 60)
    
    users = ["user1", "user2"]
    results = {}
    
    for username in users:
        print(f"\n🎯 TESTING USER: {username.upper()}")
        print("=" * 40)
        
        # Test client data
        client_result = test_user_comprehensive_data(username)
        
        # Test sales data
        sales_result = test_user_sales_data(username)
        
        # Combine results
        results[username] = {
            **client_result,
            **sales_result
        }
        
        time.sleep(2)  # Small delay between users
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS SUMMARY:")
    print("=" * 60)
    
    for username, result in results.items():
        print(f"\n👤 {username.upper()}:")
        print(f"   Client Data: {result.get('client_test', 'UNKNOWN')}")
        print(f"   Sales Data:  {result.get('sales_test', 'UNKNOWN')}")
        
        if result.get('client_test') == 'PASS':
            print(f"   Client Response Length: {result.get('client_length', 0)} chars")
        
        if result.get('sales_test') == 'PASS':
            print(f"   Sales Response Length: {result.get('sales_length', 0)} chars")
    
    # Overall assessment
    all_client_tests = [result.get('client_test') for result in results.values()]
    all_sales_tests = [result.get('sales_test') for result in results.values()]
    
    client_success = all_client_tests.count('PASS')
    sales_success = all_sales_tests.count('PASS')
    
    print(f"\n🎯 OVERALL ASSESSMENT:")
    print(f"   Client Data Success: {client_success}/{len(users)} users")
    print(f"   Sales Data Success:  {sales_success}/{len(users)} users")
    
    if client_success == len(users) and sales_success == len(users):
        print(f"\n🎉 PERFECT! ALL TESTS PASSED!")
        print("✅ All users have complete client and sales data")
        print("✅ User-specific data isolation is working")
        print("✅ Win-MCP database has been successfully enhanced")
        print("✅ The missing data issue has been COMPLETELY FIXED!")
    elif client_success == len(users):
        print(f"\n✅ CLIENT DATA: All users have client information")
        print(f"⚠️  SALES DATA: Some users missing sales data")
    elif sales_success == len(users):
        print(f"\n✅ SALES DATA: All users have sales information")
        print(f"⚠️  CLIENT DATA: Some users missing client data")
    else:
        print(f"\n⚠️  PARTIAL SUCCESS: Some data types still missing")
    
    print(f"\n📝 What was added to fix the issue:")
    print("   ✅ Added client record for user2 (Docteur Marie)")
    print("   ✅ Added 7 sales records for user2 (3001-3007)")
    print("   ✅ Added client record for admin user")
    print("   ✅ Added medical profiles for all users")
    print("   ✅ Updated MCP to use user-specific endpoints")
    print("   ✅ Fixed data formatting for single client objects")

if __name__ == "__main__":
    main()
