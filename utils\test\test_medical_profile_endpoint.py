#!/usr/bin/env python3
"""
Test the specific medical profile endpoint that should work
"""

import requests
import json

def test_medical_profile_endpoint():
    """Test the medical profile endpoint specifically"""
    print("🩺 Testing Medical Profile Endpoint")
    print("=" * 50)
    
    # Get authentication tokens
    print("Step 1: Getting authentication tokens...")
    
    # Tenant authentication
    tenant_response = requests.post(
        "http://localhost:8082/auth/tenant/login",
        json={"username": "0001", "password": "123456"},
        headers={'Content-Type': 'application/json'}
    )
    
    if tenant_response.status_code != 200:
        print(f"❌ Tenant auth failed: {tenant_response.text}")
        return
    
    tenant_token = tenant_response.json().get('token')
    print(f"✅ Tenant token obtained")
    
    # User authentication
    user_response = requests.post(
        "http://localhost:8082/auth/login",
        json={"username": "user1", "password": "password"},
        headers={
            'Content-Type': 'application/json',
            'AuthorizationTenant': f'BearerTenant {tenant_token}'
        }
    )
    
    if user_response.status_code != 200:
        print(f"❌ User auth failed: {user_response.text}")
        return
    
    user_token = user_response.json().get('token')
    print(f"✅ User token obtained")
    
    # Test the medical profile endpoint
    print("\nStep 2: Testing medical profile endpoint...")
    medical_url = "http://localhost:8082/api/winplus/medical-profile/user1"
    
    try:
        medical_response = requests.get(
            medical_url,
            headers={
                'AuthorizationTenant': f'BearerTenant {tenant_token}',
                'Authorization': f'Bearer {user_token}'
            },
            timeout=10
        )
        
        print(f"Status Code: {medical_response.status_code}")
        
        if medical_response.status_code == 200:
            try:
                data = medical_response.json()
                print(f"✅ Medical profile endpoint works!")
                print(f"Response keys: {list(data.keys())}")
                
                if data.get('medicalProfile'):
                    profile = data['medicalProfile']
                    print(f"Medical Profile Data:")
                    print(f"  - Blood Type: {profile.get('bloodType')}")
                    print(f"  - Allergies: {profile.get('allergies')}")
                    print(f"  - Doctor: {profile.get('doctorName')}")
                    print(f"  - Emergency Contact: {profile.get('emergencyContact')}")
                    return True
                else:
                    print(f"⚠️  No medical profile in response")
                    print(f"Full response: {data}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error: {e}")
                print(f"Raw response: {medical_response.text[:200]}...")
                return False
        else:
            print(f"❌ Medical profile endpoint failed: {medical_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_problematic_user_data_endpoint():
    """Test the problematic user-data endpoint"""
    print("\n🔍 Testing Problematic User-Data Endpoint")
    print("=" * 50)
    
    # Get authentication tokens
    tenant_response = requests.post(
        "http://localhost:8082/auth/tenant/login",
        json={"username": "0001", "password": "123456"},
        headers={'Content-Type': 'application/json'}
    )
    
    if tenant_response.status_code != 200:
        print(f"❌ Tenant auth failed")
        return False
    
    tenant_token = tenant_response.json().get('token')
    
    user_response = requests.post(
        "http://localhost:8082/auth/login",
        json={"username": "user1", "password": "password"},
        headers={
            'Content-Type': 'application/json',
            'AuthorizationTenant': f'BearerTenant {tenant_token}'
        }
    )
    
    if user_response.status_code != 200:
        print(f"❌ User auth failed")
        return False
    
    user_token = user_response.json().get('token')
    
    # Test the problematic user-data endpoint
    print("Testing problematic user-data endpoint...")
    user_data_url = "http://localhost:8082/api/winplus/user-data/user1"
    
    try:
        user_data_response = requests.get(
            user_data_url,
            headers={
                'AuthorizationTenant': f'BearerTenant {tenant_token}',
                'Authorization': f'Bearer {user_token}'
            },
            timeout=10
        )
        
        print(f"Status Code: {user_data_response.status_code}")
        
        if user_data_response.status_code == 200:
            try:
                data = user_data_response.json()
                print(f"✅ User-data endpoint works! (unexpected)")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON decode error (expected): {str(e)[:100]}...")
                return False
        else:
            print(f"❌ User-data endpoint failed: {user_data_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error (expected): {str(e)[:100]}...")
        return False

def main():
    print("🔍 TESTING MEDICAL PROFILE ENDPOINTS")
    print("=" * 60)
    
    # Test the working medical profile endpoint
    medical_works = test_medical_profile_endpoint()
    
    # Test the problematic user-data endpoint
    user_data_works = test_problematic_user_data_endpoint()
    
    print("\n" + "=" * 60)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"Medical Profile Endpoint: {'✅ WORKS' if medical_works else '❌ FAILS'}")
    print(f"User Data Endpoint: {'✅ WORKS' if user_data_works else '❌ FAILS (expected)'}")
    
    if medical_works:
        print(f"\n🎉 SOLUTION FOUND!")
        print(f"✅ Use the medical profile endpoint: /api/winplus/medical-profile/{{username}}")
        print(f"❌ Avoid the user-data endpoint: /api/winplus/user-data/{{username}} (has circular references)")
        
        print(f"\n📋 WORKING LOGIN CREDENTIALS:")
        print(f"Username: user1")
        print(f"Password: password")
        print(f"Username: user2") 
        print(f"Password: password")
        
        print(f"\n🔗 WORKING API ENDPOINT:")
        print(f"GET http://localhost:8082/api/winplus/medical-profile/user1")
        print(f"Headers:")
        print(f"  AuthorizationTenant: BearerTenant {{tenant_token}}")
        print(f"  Authorization: Bearer {{user_token}}")
    else:
        print(f"\n⚠️  Both endpoints have issues - need to investigate further")

if __name__ == "__main__":
    main()
