package com.chatbootmcp.chatmcp.controller;

import com.chatbootmcp.chatmcp.dto.request.LoginRequest;
import com.chatbootmcp.chatmcp.dto.request.TestLoginRequest;
import com.chatbootmcp.chatmcp.dto.response.AuthResponse;
import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.repository.UserRepository;
import com.chatbootmcp.chatmcp.service.AuthService;
import com.chatbootmcp.chatmcp.util.JwtUtil;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        return ResponseEntity.ok(authService.login(loginRequest));
    }

    /**
     * Tenant authentication endpoint for pharmacy/tenant login
     * This endpoint is used for the first step of dual authentication
     */
    @PostMapping("/tenant/login")
    public ResponseEntity<AuthResponse> tenantLogin(@Valid @RequestBody LoginRequest loginRequest) {
        System.out.println("🏥 WIN-MCP: Tenant authentication request for: " + loginRequest.getUsername());
        return ResponseEntity.ok(authService.tenantLogin(loginRequest));
    }

    // Rest of the controller methods remain the same
    // ...
}
