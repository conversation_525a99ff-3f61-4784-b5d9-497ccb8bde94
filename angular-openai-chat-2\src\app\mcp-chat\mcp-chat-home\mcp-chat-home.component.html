<!-- Professional Clean MCP Home Page Structure -->
<div class="home-container app-mcp-chat-home-root">
  <!-- Hero Section with Clean Welcome -->
  <section class="hero-section" aria-label="Section d'accueil MCP">
    <!-- Welcome Message -->
    <div class="welcome-content">
      <h1 class="welcome-title">Bienvenue sur WinPharm+ Assistant 🤖</h1>
      <p class="welcome-subtitle">Je peux vous aider avec vos informations personnelles et répondre à vos questions générales.</p>
      <p class="info-badge">💡 Pour les questions sur vos données personnelles, choisissez l'option <b>Global</b> quand je vous le demanderai.</p>
    </div>

    <!-- User Info Section -->
    <div *ngIf="isLoggedIn()" class="user-info-card">
      <div class="user-details">
        <div class="user-avatar">
          <i class="mdi mdi-account-circle" aria-hidden="true"></i>
        </div>
        <div class="user-text">
          <span class="username">{{ getCurrentUsername() }}</span>
          <span class="backend-badge">{{ getCurrentBackend() }}</span>
        </div>
      </div>
      <button class="logout-btn"
              (click)="onLogout()"
              aria-label="Se déconnecter"
              type="button">
        <i class="mdi mdi-logout" aria-hidden="true"></i>
        <span>Déconnexion</span>
      </button>
    </div>

    <!-- Main Action Button -->
    <button class="start-chat-button"
            (click)="onAskQuestion()"
            aria-label="Commencer une nouvelle conversation MCP">
      <div class="button-content">
        <i class="mdi mdi-message-text-outline" aria-hidden="true"></i>
        <span>Commencer une conversation</span>
      </div>
    </button>
  </section>

  <!-- Quick Actions Section -->
  <section class="quick-actions-section" aria-label="Actions rapides MCP">
    <h2 class="section-title">Actions rapides</h2>

    <div class="action-grid">
      <button class="action-card"
              (click)="onSelectQuestion('Quelles sont mes informations personnelles ?')"
              aria-label="Consulter mes données personnelles">
        <div class="action-icon">
          <i class="mdi mdi-account-details" aria-hidden="true"></i>
        </div>
        <div class="action-content">
          <h3>Mes données</h3>
          <p>Consultez vos informations personnelles</p>
        </div>
      </button>

      <button class="action-card"
              (click)="onSelectQuestion('Montrez-moi mes transactions récentes')"
              aria-label="Voir mes transactions">
        <div class="action-icon">
          <i class="mdi mdi-credit-card-outline" aria-hidden="true"></i>
        </div>
        <div class="action-content">
          <h3>Transactions</h3>
          <p>Consultez vos transactions récentes</p>
        </div>
      </button>

      <button class="action-card"
              (click)="onSelectQuestion('Pouvez-vous m\'aider avec une question générale ?')"
              aria-label="Poser une question générale">
        <div class="action-icon">
          <i class="mdi mdi-help-circle-outline" aria-hidden="true"></i>
        </div>
        <div class="action-content">
          <h3>Questions générales</h3>
          <p>Obtenez de l'aide sur tout sujet</p>
        </div>
      </button>

      <button class="action-card"
              (click)="onNavigateToHelp()"
              aria-label="Accéder au centre d'aide">
        <div class="action-icon">
          <i class="mdi mdi-lifebuoy" aria-hidden="true"></i>
        </div>
        <div class="action-content">
          <h3>Centre d'aide</h3>
          <p>Guides et documentation</p>
        </div>
      </button>
    </div>
  </section>

  <!-- Popular Topics Section -->
  <section class="popular-topics-section" aria-label="Sujets populaires MCP">
    <h2 class="section-title">Questions fréquentes</h2>

    <div class="topics-list">
      <button *ngFor="let question of suggestedQuestions"
              class="topic-item"
              (click)="onSelectQuestion(question)"
              [attr.aria-label]="'Poser la question: ' + question">
        <span class="topic-text">{{ question }}</span>
        <i class="mdi mdi-chevron-right topic-arrow" aria-hidden="true"></i>
      </button>
    </div>
  </section>
</div>
