#!/usr/bin/env python3
"""
Test the new tenant credentials: 0001/123456
"""

import requests
import json

def test_new_tenant_credentials():
    """Test the new tenant credentials"""
    print("🔐 Testing New Tenant Credentials: 0001/123456")
    print("=" * 60)
    
    # Test tenant authentication with new credentials
    print("Step 1: Testing tenant authentication...")
    tenant_url = "http://localhost:8082/auth/tenant/login"
    tenant_payload = {
        "username": "0001",
        "password": "123456"
    }
    
    try:
        tenant_response = requests.post(tenant_url, json=tenant_payload, headers={'Content-Type': 'application/json'})
        print(f"Tenant Status: {tenant_response.status_code}")
        
        if tenant_response.status_code == 200:
            tenant_data = tenant_response.json()
            tenant_token = tenant_data.get('token')
            print(f"✅ Tenant authentication successful! Token: {tenant_token[:20]}...")
            
            # Test user authentication with existing users
            print("\nStep 2: Testing user authentication with existing users...")
            test_users = ["user1", "user2"]
            
            for username in test_users:
                print(f"\nTesting user: {username}")
                user_url = "http://localhost:8082/auth/login"
                user_payload = {
                    "username": username,
                    "password": "password"
                }
                user_headers = {
                    'Content-Type': 'application/json',
                    'AuthorizationTenant': f'BearerTenant {tenant_token}'
                }
                
                try:
                    user_response = requests.post(user_url, json=user_payload, headers=user_headers)
                    print(f"  User Status: {user_response.status_code}")
                    
                    if user_response.status_code == 200:
                        user_data = user_response.json()
                        user_token = user_data.get('token')
                        print(f"  ✅ User {username} authentication successful! Token: {user_token[:20]}...")
                        
                        # Test medical profile
                        print(f"  Testing medical profile for {username}...")
                        medical_url = f"http://localhost:8082/api/winplus/medical-profile/{username}"
                        medical_headers = {
                            'AuthorizationTenant': f'BearerTenant {tenant_token}',
                            'Authorization': f'Bearer {user_token}'
                        }
                        
                        medical_response = requests.get(medical_url, headers=medical_headers)
                        if medical_response.status_code == 200:
                            medical_data = medical_response.json()
                            if medical_data.get('medicalProfile'):
                                profile = medical_data['medicalProfile']
                                print(f"  ✅ Medical profile found - Blood Type: {profile.get('bloodType')}")
                            else:
                                print(f"  ⚠️  No medical profile found")
                        else:
                            print(f"  ❌ Medical profile failed: {medical_response.status_code}")
                    else:
                        print(f"  ❌ User {username} authentication failed: {user_response.text}")
                        
                except Exception as e:
                    print(f"  ❌ Error testing user {username}: {e}")
            
            return True
        else:
            print(f"❌ Tenant authentication failed: {tenant_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during tenant authentication: {e}")
        return False

def test_old_tenant_credentials():
    """Test if old tenant credentials still work"""
    print("\n🔍 Testing Old Tenant Credentials: pharmacy_tenant/tenant_password")
    print("=" * 60)
    
    tenant_url = "http://localhost:8082/auth/tenant/login"
    tenant_payload = {
        "username": "pharmacy_tenant",
        "password": "tenant_password"
    }
    
    try:
        tenant_response = requests.post(tenant_url, json=tenant_payload, headers={'Content-Type': 'application/json'})
        print(f"Old Tenant Status: {tenant_response.status_code}")
        
        if tenant_response.status_code == 200:
            print(f"✅ Old tenant credentials still work")
            return True
        else:
            print(f"❌ Old tenant credentials don't work: {tenant_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🔐 TESTING NEW TENANT CREDENTIALS")
    print("=" * 70)
    
    # Test new tenant credentials
    new_works = test_new_tenant_credentials()
    
    # Test old tenant credentials
    old_works = test_old_tenant_credentials()
    
    print("\n" + "=" * 70)
    print("🎯 TENANT CREDENTIALS TEST SUMMARY")
    print("=" * 70)
    print(f"New Credentials (0001/123456): {'✅ WORKS' if new_works else '❌ FAILS'}")
    print(f"Old Credentials (pharmacy_tenant/tenant_password): {'✅ WORKS' if old_works else '❌ FAILS'}")
    
    if new_works:
        print(f"\n🎉 NEW TENANT CREDENTIALS ARE WORKING!")
        print(f"✅ Tenant: 0001 / 123456")
        print(f"✅ Users: user1/password, user2/password")
        print(f"✅ Medical profiles available for both users")
        
        print(f"\n📋 FRONTEND CONFIGURATION:")
        print(f"Update Angular environment.ts:")
        print(f"  tenant: {{")
        print(f"    username: '0001',")
        print(f"    password: '123456'")
        print(f"  }}")
        
        print(f"\n🔗 WORKING AUTHENTICATION FLOW:")
        print(f"1. Tenant Auth: POST /auth/tenant/login")
        print(f"   Body: {{\"username\":\"0001\",\"password\":\"123456\"}}")
        print(f"2. User Auth: POST /auth/login")
        print(f"   Body: {{\"username\":\"user1\",\"password\":\"password\"}}")
        print(f"   Headers: AuthorizationTenant: BearerTenant {{tenant_token}}")
    else:
        print(f"\n⚠️  NEW TENANT CREDENTIALS NOT WORKING")
        print(f"Need to investigate further...")

if __name__ == "__main__":
    main()
