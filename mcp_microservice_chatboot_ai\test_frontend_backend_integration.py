#!/usr/bin/env python3
"""
Comprehensive test script for Frontend-Backend Dual Authentication Integration
Tests the complete flow from Angular frontend to backend authentication
"""

import requests
import json
import time

def test_chat_mcp_single_auth():
    """Test Chat-MCP single authentication"""
    print("🔐 Testing Chat-MCP Single Authentication")
    print("=" * 50)
    
    url = "http://localhost:8080/api/auth/login"
    payload = {
        "username": "user1",
        "password": "password"
    }
    
    try:
        response = requests.post(url, json=payload, headers={'Content-Type': 'application/json'})
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            print(f"✅ Chat-MCP Single Authentication successful! Token: {token[:20]}...")
            return token
        else:
            print(f"❌ Chat-MCP Single Authentication failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error during Chat-MCP authentication: {e}")
        return None

def test_win_mcp_dual_auth():
    """Test Win-MCP dual authentication"""
    print("\n🔐 Testing Win-MCP Dual Authentication")
    print("=" * 50)
    
    # Step 1: Tenant Authentication
    print("🏥 Step 1: Tenant Authentication...")
    tenant_url = "http://localhost:8082/auth/tenant/login"
    tenant_payload = {
        "username": "0001",
        "password": "123456"
    }
    
    try:
        tenant_response = requests.post(tenant_url, json=tenant_payload, headers={'Content-Type': 'application/json'})
        print(f"Tenant Status Code: {tenant_response.status_code}")
        print(f"Tenant Response: {tenant_response.text}")
        
        if tenant_response.status_code != 200:
            print("❌ Tenant authentication failed")
            return None, None
            
        tenant_data = tenant_response.json()
        tenant_token = tenant_data.get('token')
        print(f"✅ Tenant authentication successful! Token: {tenant_token[:20]}...")
        
        # Step 2: User Authentication with Tenant Token
        print("\n👤 Step 2: User Authentication with Tenant Token...")
        user_url = "http://localhost:8082/auth/login"
        user_payload = {
            "username": "user1",
            "password": "password"
        }
        
        user_headers = {
            'Content-Type': 'application/json',
            'AuthorizationTenant': f'BearerTenant {tenant_token}'
        }
        
        user_response = requests.post(user_url, json=user_payload, headers=user_headers)
        print(f"User Status Code: {user_response.status_code}")
        print(f"User Response: {user_response.text}")
        
        if user_response.status_code == 200:
            user_data = user_response.json()
            user_token = user_data.get('token')
            print(f"✅ User authentication successful! Token: {user_token[:20]}...")
            return tenant_token, user_token
        else:
            print(f"❌ User authentication failed: {user_response.text}")
            return tenant_token, None
            
    except Exception as e:
        print(f"❌ Error during Win-MCP dual authentication: {e}")
        return None, None

def test_mcp_microservice_with_backends():
    """Test MCP microservice with different backends"""
    print("\n🧠 Testing MCP Microservice with Different Backends")
    print("=" * 60)
    
    # Test medical profile question
    url = "http://localhost:8081/api/chat"
    
    # Test 1: Ask medical question
    print("📋 Test 1: Asking medical question...")
    request1 = {
        "conversationId": "test-backend-integration",
        "content": "quelles sont mes informations médicales personnelles?",
        "username": "user1"
    }
    
    response1 = requests.post(url, json=request1, headers={'Content-Type': 'application/json'})
    print(f"Status Code: {response1.status_code}")
    print(f"Response: {response1.text}")
    
    if response1.status_code != 200:
        print("❌ Failed to ask medical question")
        return False
    
    # Wait a moment for processing
    time.sleep(2)
    
    # Test 2: Select database source (option 1)
    print("\n🔢 Test 2: Selecting database source (option 1)...")
    request2 = {
        "conversationId": "test-backend-integration",
        "content": "1",
        "username": "user1"
    }
    
    response2 = requests.post(url, json=request2, headers={'Content-Type': 'application/json'})
    print(f"Status Code: {response2.status_code}")
    print(f"Response: {response2.text}")
    
    if response2.status_code == 200:
        response_data = response2.json()
        content = response_data.get('content', '')
        
        # Check if we got medical profile data
        if any(keyword in content.lower() for keyword in ['groupe sanguin', 'allergies', 'médicaments', 'médecin']):
            print("✅ MCP microservice test successful with medical data!")
            return True
        else:
            print("⚠️  MCP microservice responded but no medical data found")
            return False
    else:
        print("❌ Failed to get response from MCP microservice")
        return False

def test_api_with_dual_auth(tenant_token, user_token):
    """Test direct API call with dual authentication"""
    print("\n🔐 Testing Direct API Call with Dual Authentication")
    print("=" * 55)
    
    url = "http://localhost:8082/api/winplus/medical-profile/user1"
    
    headers = {
        'Content-Type': 'application/json',
        'AuthorizationTenant': f'BearerTenant {tenant_token}',
        'Authorization': f'Bearer {user_token}'
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Direct API call with dual authentication successful!")
            return True
        else:
            print(f"❌ Direct API call failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error during direct API call: {e}")
        return False

def main():
    print("🎯 COMPREHENSIVE FRONTEND-BACKEND DUAL AUTHENTICATION TEST")
    print("=" * 70)
    print("Testing complete integration between Angular frontend and backend authentication")
    print()
    
    # Test Chat-MCP single authentication
    chat_mcp_token = test_chat_mcp_single_auth()
    
    # Test Win-MCP dual authentication
    tenant_token, user_token = test_win_mcp_dual_auth()
    
    # Test MCP microservice
    mcp_success = test_mcp_microservice_with_backends()
    
    # Test direct API with dual auth
    api_success = False
    if tenant_token and user_token:
        api_success = test_api_with_dual_auth(tenant_token, user_token)
    
    # Summary
    print("\n" + "=" * 70)
    print("🎯 COMPREHENSIVE TEST SUMMARY:")
    print(f"   🔑 Chat-MCP Single Auth: {'✅ PASS' if chat_mcp_token else '❌ FAIL'}")
    print(f"   🏥 Win-MCP Tenant Auth: {'✅ PASS' if tenant_token else '❌ FAIL'}")
    print(f"   👤 Win-MCP User Auth: {'✅ PASS' if user_token else '❌ FAIL'}")
    print(f"   🔐 Win-MCP Dual Auth: {'✅ PASS' if (tenant_token and user_token) else '❌ FAIL'}")
    print(f"   🧠 MCP Microservice: {'✅ PASS' if mcp_success else '❌ FAIL'}")
    print(f"   🌐 Direct API Call: {'✅ PASS' if api_success else '❌ FAIL'}")
    
    all_tests_passed = all([
        chat_mcp_token,
        tenant_token,
        user_token,
        mcp_success,
        api_success
    ])
    
    print("\n" + "=" * 70)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Frontend-Backend Dual Authentication Integration is working perfectly!")
        print("✅ Both Chat-MCP (single auth) and Win-MCP (dual auth) are functional!")
        print("✅ MCP microservice correctly handles both authentication types!")
        print("✅ Medical profile data retrieval is working correctly!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the logs above for details.")
    
    print("\n📋 AUTHENTICATION SUMMARY:")
    print("   • Chat-MCP: Single Bearer token authentication")
    print("   • Win-MCP: Dual authentication (BearerTenant + Bearer tokens)")
    print("   • Frontend: Angular with backend selection and dual auth support")
    print("   • MCP Microservice: Automatic backend routing based on configuration")

if __name__ == "__main__":
    main()
