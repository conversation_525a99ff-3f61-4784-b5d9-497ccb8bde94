package com.chatbootmcp.chatmcp.config;

import com.chatbootmcp.chatmcp.entity.*;
import com.chatbootmcp.chatmcp.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Configuration
public class DataInitializer {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ProduitRepository produitRepository;

    @Autowired
    private EnteteVenteRepository enteteVenteRepository;

    @Autowired
    private DepotRepository depotRepository;

    @Autowired
    private OperateurRepository operateurRepository;

    @Autowired
    private StockRepository stockRepository;

    @Autowired
    private FournisseurRepository fournisseurRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private PersonalMedicalProfileRepository personalMedicalProfileRepository;

    @Bean
    public CommandLineRunner initData() {
        return args -> {
            System.out.println("DataInitializer: Starting WinPlus data initialization...");

            // Create test users if they don't exist
            long userCount = userRepository.count();
            System.out.println("DataInitializer: Current user count: " + userCount);

            if (userCount == 0) {
                System.out.println("DataInitializer: Creating test users...");
                createTestUser("user1", "<EMAIL>", "password");
                createTestUser("user2", "<EMAIL>", "password");
                createTestUser("admin", "<EMAIL>", "admin", true);
                createTestUser("testuser", "<EMAIL>", "password");

                System.out.println("DataInitializer: Test users created successfully!");
            } else {
                System.out.println("DataInitializer: Users already exist, skipping initialization.");
            }

            // Initialize WinPlus sample data
            initializeWinPlusData();
        };
    }

    private void createTestUser(String username, String email, String password) {
        createTestUser(username, email, password, false);
    }

    private void createTestUser(String username, String email, String password, boolean isAdmin) {
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setPassword(passwordEncoder.encode(password));
        user.setFullName("Test " + username.substring(0, 1).toUpperCase() + username.substring(1));
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());

        Set<String> roles = new HashSet<>();
        roles.add("USER");
        if (isAdmin) {
            roles.add("ADMIN");
        }
        user.setRoles(roles);

        userRepository.save(user);
    }

    private void initializeWinPlusData() {
        System.out.println("DataInitializer: Initializing WinPlus sample data...");

        // Create sample clients
        if (clientRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample clients...");
            createSampleClients();
        }

        // Create sample products
        if (produitRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample products...");
            createSampleProducts();
        }

        // Create sample depots
        if (depotRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample depots...");
            createSampleDepots();
        }

        // Create sample operators
        if (operateurRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample operators...");
            createSampleOperators();
        }

        // Create sample suppliers
        if (fournisseurRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample suppliers...");
            createSampleSuppliers();
        }

        // Create sample sales
        if (enteteVenteRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample sales...");
            createSampleSales();
        }

        // Create sample stock
        if (stockRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample stock...");
            createSampleStock();
        }

        // Create sample medical profiles
        if (personalMedicalProfileRepository.count() == 0) {
            System.out.println("DataInitializer: Creating sample medical profiles...");
            createSampleMedicalProfiles();
        }

        System.out.println("DataInitializer: WinPlus sample data initialization completed!");
    }

    private void createSampleClients() {
        // Client 1
        Client client1 = new Client();
        client1.setCodeClient("CLI001");
        client1.setNom("Dupont");
        client1.setPrenom("Jean");
        client1.setEmail("<EMAIL>");
        client1.setGsm("**********");
        client1.setAdr1("123 Rue de la Paix");
        client1.setAdr2("Apt 4B");
        client1.setEstActif(true);
        client1.setSoldeClient(new BigDecimal("1500.00"));
        client1.setPlafondCredit(new BigDecimal("5000.00"));
        client1.setCaClient(new BigDecimal("12500.00"));
        client1.setTauxRemise(new BigDecimal("5.0"));
        clientRepository.save(client1);

        // Client 2
        Client client2 = new Client();
        client2.setCodeClient("CLI002");
        client2.setNom("Martin");
        client2.setPrenom("Marie");
        client2.setEmail("<EMAIL>");
        client2.setGsm("0623456789");
        client2.setAdr1("456 Avenue des Champs");
        client2.setEstActif(true);
        client2.setSoldeClient(new BigDecimal("-250.00"));
        client2.setPlafondCredit(new BigDecimal("3000.00"));
        client2.setCaClient(new BigDecimal("8750.00"));
        client2.setTauxRemise(new BigDecimal("3.0"));
        clientRepository.save(client2);

        // Client 3 - testuser
        Client client3 = new Client();
        client3.setCodeClient("testuser");
        client3.setNom("Benali");
        client3.setPrenom("Ahmed");
        client3.setEmail("<EMAIL>");
        client3.setGsm("0634567890");
        client3.setAdr1("789 Boulevard Mohammed V");
        client3.setEstActif(true);
        client3.setSoldeClient(new BigDecimal("750.00"));
        client3.setPlafondCredit(new BigDecimal("4000.00"));
        client3.setCaClient(new BigDecimal("15200.00"));
        client3.setTauxRemise(new BigDecimal("7.5"));
        clientRepository.save(client3);

        // Client 4 - user1 (for MCP testing)
        Client client4 = new Client();
        client4.setCodeClient("user1");
        client4.setNom("Pharmacien");
        client4.setPrenom("Test");
        client4.setEmail("<EMAIL>");
        client4.setGsm("+33 1 23 45 67 89");
        client4.setAdr1("123 Rue de la Santé");
        client4.setAdr2("Pharmacie Centrale");
        client4.setEstActif(true);
        client4.setSoldeClient(new BigDecimal("2850.75"));
        client4.setPlafondCredit(new BigDecimal("10000.00"));
        client4.setCaClient(new BigDecimal("45600.00"));
        client4.setTauxRemise(new BigDecimal("12.5"));
        clientRepository.save(client4);

        // Client 5 - user2 (for MCP testing)
        Client client5 = new Client();
        client5.setCodeClient("user2");
        client5.setNom("Docteur");
        client5.setPrenom("Marie");
        client5.setEmail("<EMAIL>");
        client5.setGsm("+33 4 56 78 90 12");
        client5.setAdr1("45 Avenue des Fleurs");
        client5.setAdr2("Pharmacie du Marché");
        client5.setEstActif(true);
        client5.setSoldeClient(new BigDecimal("1750.50"));
        client5.setPlafondCredit(new BigDecimal("8000.00"));
        client5.setCaClient(new BigDecimal("32400.00"));
        client5.setTauxRemise(new BigDecimal("10.0"));
        clientRepository.save(client5);

        // Client 6 - admin (for MCP testing)
        Client client6 = new Client();
        client6.setCodeClient("admin");
        client6.setNom("Administrateur");
        client6.setPrenom("Système");
        client6.setEmail("<EMAIL>");
        client6.setGsm("+33 1 00 00 00 00");
        client6.setAdr1("1 Place de l'Administration");
        client6.setAdr2("Bureau Principal");
        client6.setEstActif(true);
        client6.setSoldeClient(new BigDecimal("5000.00"));
        client6.setPlafondCredit(new BigDecimal("20000.00"));
        client6.setCaClient(new BigDecimal("75000.00"));
        client6.setTauxRemise(new BigDecimal("15.0"));
        clientRepository.save(client6);
    }

    private void createSampleProducts() {
        // Product 1 - Paracetamol
        Produit produit1 = new Produit();
        produit1.setCodePrd("MED001");
        produit1.setDesignation("Paracétamol 500mg");
        produit1.setDosage("500mg");
        produit1.setPresentation("Boîte de 20 comprimés");
        produit1.setCodeBarre("3401234567890");
        produit1.setPrixVenteStd(new BigDecimal("25.50"));
        produit1.setPrixAchatStd(new BigDecimal("18.00"));
        produit1.setTotalStock(new BigDecimal("150"));
        produit1.setEstVendable(true);
        produit1.setEstStockable(true);
        produit1.setEstOblgPrescription(false);
        produit1.setEstPsychotrope(false);
        produit1.setTauxRemb(new BigDecimal("70.0"));
        produitRepository.save(produit1);

        // Product 2 - Antibiotique
        Produit produit2 = new Produit();
        produit2.setCodePrd("MED002");
        produit2.setDesignation("Amoxicilline 1g");
        produit2.setDosage("1g");
        produit2.setPresentation("Boîte de 12 gélules");
        produit2.setCodeBarre("3401234567891");
        produit2.setPrixVenteStd(new BigDecimal("45.80"));
        produit2.setPrixAchatStd(new BigDecimal("32.50"));
        produit2.setTotalStock(new BigDecimal("75"));
        produit2.setEstVendable(true);
        produit2.setEstStockable(true);
        produit2.setEstOblgPrescription(true);
        produit2.setEstPsychotrope(false);
        produit2.setTauxRemb(new BigDecimal("100.0"));
        produitRepository.save(produit2);

        // Product 3 - Vitamines
        Produit produit3 = new Produit();
        produit3.setCodePrd("VIT001");
        produit3.setDesignation("Vitamine C 1000mg");
        produit3.setDosage("1000mg");
        produit3.setPresentation("Boîte de 30 comprimés effervescents");
        produit3.setCodeBarre("3401234567892");
        produit3.setPrixVenteStd(new BigDecimal("35.20"));
        produit3.setPrixAchatStd(new BigDecimal("24.80"));
        produit3.setTotalStock(new BigDecimal("200"));
        produit3.setEstVendable(true);
        produit3.setEstStockable(true);
        produit3.setEstOblgPrescription(false);
        produit3.setEstPsychotrope(false);
        produit3.setTauxRemb(new BigDecimal("0.0"));
        produitRepository.save(produit3);
    }

    private void createSampleSales() {
        // Get sample data
        Client client1 = clientRepository.findByCodeClient("CLI001").orElse(null);
        Client client2 = clientRepository.findByCodeClient("CLI002").orElse(null);
        Client testClient = clientRepository.findByCodeClient("testuser").orElse(null);
        Client user1Client = clientRepository.findByCodeClient("user1").orElse(null);

        if (client1 != null) {
            // Sale 1
            EnteteVente vente1 = new EnteteVente();
            vente1.setClient(client1);
            vente1.setDateVente(LocalDateTime.now().minusDays(5));
            vente1.setNumVente(1001L);
            vente1.setMntBrutTtc(new BigDecimal("51.00"));
            vente1.setMntNetTtc(new BigDecimal("48.45"));
            vente1.setMntRemiseTtc(new BigDecimal("2.55"));
            vente1.setTauxRemise(new BigDecimal("5.0"));
            vente1.setTotalQte(new BigDecimal("2"));
            vente1.setNbrLignes(1);
            vente1.setNbrPrd(1);
            vente1.setMntEncaisse(new BigDecimal("48.45"));
            enteteVenteRepository.save(vente1);
        }

        if (client2 != null) {
            // Sale 2
            EnteteVente vente2 = new EnteteVente();
            vente2.setClient(client2);
            vente2.setDateVente(LocalDateTime.now().minusDays(2));
            vente2.setNumVente(1002L);
            vente2.setMntBrutTtc(new BigDecimal("45.80"));
            vente2.setMntNetTtc(new BigDecimal("44.42"));
            vente2.setMntRemiseTtc(new BigDecimal("1.38"));
            vente2.setTauxRemise(new BigDecimal("3.0"));
            vente2.setTotalQte(new BigDecimal("1"));
            vente2.setNbrLignes(1);
            vente2.setNbrPrd(1);
            vente2.setMntEncaisse(new BigDecimal("44.42"));
            enteteVenteRepository.save(vente2);
        }

        if (testClient != null) {
            // Sale 3 for testuser
            EnteteVente vente3 = new EnteteVente();
            vente3.setClient(testClient);
            vente3.setDateVente(LocalDateTime.now().minusDays(1));
            vente3.setNumVente(1003L);
            vente3.setMntBrutTtc(new BigDecimal("105.60"));
            vente3.setMntNetTtc(new BigDecimal("97.68"));
            vente3.setMntRemiseTtc(new BigDecimal("7.92"));
            vente3.setTauxRemise(new BigDecimal("7.5"));
            vente3.setTotalQte(new BigDecimal("3"));
            vente3.setNbrLignes(2);
            vente3.setNbrPrd(2);
            vente3.setMntEncaisse(new BigDecimal("97.68"));
            enteteVenteRepository.save(vente3);
        }

        // Create comprehensive sales data for user1 (MCP testing)
        if (user1Client != null) {
            // Sale 4 - Recent large sale
            EnteteVente vente4 = new EnteteVente();
            vente4.setClient(user1Client);
            vente4.setDateVente(LocalDateTime.now().minusDays(1));
            vente4.setNumVente(2001L);
            vente4.setMntBrutTtc(new BigDecimal("1250.00"));
            vente4.setMntNetTtc(new BigDecimal("1093.75"));
            vente4.setMntRemiseTtc(new BigDecimal("156.25"));
            vente4.setTauxRemise(new BigDecimal("12.5"));
            vente4.setTotalQte(new BigDecimal("25"));
            vente4.setNbrLignes(5);
            vente4.setNbrPrd(5);
            vente4.setMntEncaisse(new BigDecimal("1093.75"));
            enteteVenteRepository.save(vente4);

            // Sale 5 - Medium sale
            EnteteVente vente5 = new EnteteVente();
            vente5.setClient(user1Client);
            vente5.setDateVente(LocalDateTime.now().minusDays(3));
            vente5.setNumVente(2002L);
            vente5.setMntBrutTtc(new BigDecimal("680.50"));
            vente5.setMntNetTtc(new BigDecimal("595.44"));
            vente5.setMntRemiseTtc(new BigDecimal("85.06"));
            vente5.setTauxRemise(new BigDecimal("12.5"));
            vente5.setTotalQte(new BigDecimal("15"));
            vente5.setNbrLignes(3);
            vente5.setNbrPrd(3);
            vente5.setMntEncaisse(new BigDecimal("595.44"));
            enteteVenteRepository.save(vente5);

            // Sale 6 - Small sale
            EnteteVente vente6 = new EnteteVente();
            vente6.setClient(user1Client);
            vente6.setDateVente(LocalDateTime.now().minusDays(7));
            vente6.setNumVente(2003L);
            vente6.setMntBrutTtc(new BigDecimal("125.80"));
            vente6.setMntNetTtc(new BigDecimal("110.08"));
            vente6.setMntRemiseTtc(new BigDecimal("15.72"));
            vente6.setTauxRemise(new BigDecimal("12.5"));
            vente6.setTotalQte(new BigDecimal("4"));
            vente6.setNbrLignes(2);
            vente6.setNbrPrd(2);
            vente6.setMntEncaisse(new BigDecimal("110.08"));
            enteteVenteRepository.save(vente6);

            // Sale 7 - Older sale
            EnteteVente vente7 = new EnteteVente();
            vente7.setClient(user1Client);
            vente7.setDateVente(LocalDateTime.now().minusDays(15));
            vente7.setNumVente(2004L);
            vente7.setMntBrutTtc(new BigDecimal("890.25"));
            vente7.setMntNetTtc(new BigDecimal("778.97"));
            vente7.setMntRemiseTtc(new BigDecimal("111.28"));
            vente7.setTauxRemise(new BigDecimal("12.5"));
            vente7.setTotalQte(new BigDecimal("18"));
            vente7.setNbrLignes(4);
            vente7.setNbrPrd(4);
            vente7.setMntEncaisse(new BigDecimal("778.97"));
            enteteVenteRepository.save(vente7);
        }

        // Create comprehensive sales data for user2 (MCP testing)
        Client user2Client = clientRepository.findByCodeClient("user2").orElse(null);
        if (user2Client != null) {
            // Sale 8 - Recent sale for user2
            EnteteVente vente8 = new EnteteVente();
            vente8.setClient(user2Client);
            vente8.setDateVente(LocalDateTime.now().minusDays(2));
            vente8.setNumVente(3001L);
            vente8.setMntBrutTtc(new BigDecimal("850.00"));
            vente8.setMntNetTtc(new BigDecimal("765.00"));
            vente8.setMntRemiseTtc(new BigDecimal("85.00"));
            vente8.setTauxRemise(new BigDecimal("10.0"));
            vente8.setTotalQte(new BigDecimal("20"));
            vente8.setNbrLignes(4);
            vente8.setNbrPrd(4);
            vente8.setMntEncaisse(new BigDecimal("765.00"));
            enteteVenteRepository.save(vente8);

            // Sale 9 - Medium sale for user2
            EnteteVente vente9 = new EnteteVente();
            vente9.setClient(user2Client);
            vente9.setDateVente(LocalDateTime.now().minusDays(5));
            vente9.setNumVente(3002L);
            vente9.setMntBrutTtc(new BigDecimal("420.50"));
            vente9.setMntNetTtc(new BigDecimal("378.45"));
            vente9.setMntRemiseTtc(new BigDecimal("42.05"));
            vente9.setTauxRemise(new BigDecimal("10.0"));
            vente9.setTotalQte(new BigDecimal("12"));
            vente9.setNbrLignes(3);
            vente9.setNbrPrd(3);
            vente9.setMntEncaisse(new BigDecimal("378.45"));
            enteteVenteRepository.save(vente9);

            // Sale 10 - Large sale for user2
            EnteteVente vente10 = new EnteteVente();
            vente10.setClient(user2Client);
            vente10.setDateVente(LocalDateTime.now().minusDays(8));
            vente10.setNumVente(3003L);
            vente10.setMntBrutTtc(new BigDecimal("1200.00"));
            vente10.setMntNetTtc(new BigDecimal("1080.00"));
            vente10.setMntRemiseTtc(new BigDecimal("120.00"));
            vente10.setTauxRemise(new BigDecimal("10.0"));
            vente10.setTotalQte(new BigDecimal("30"));
            vente10.setNbrLignes(6);
            vente10.setNbrPrd(6);
            vente10.setMntEncaisse(new BigDecimal("1080.00"));
            enteteVenteRepository.save(vente10);

            // Sale 11 - Small sale for user2
            EnteteVente vente11 = new EnteteVente();
            vente11.setClient(user2Client);
            vente11.setDateVente(LocalDateTime.now().minusDays(12));
            vente11.setNumVente(3004L);
            vente11.setMntBrutTtc(new BigDecimal("180.75"));
            vente11.setMntNetTtc(new BigDecimal("162.68"));
            vente11.setMntRemiseTtc(new BigDecimal("18.07"));
            vente11.setTauxRemise(new BigDecimal("10.0"));
            vente11.setTotalQte(new BigDecimal("6"));
            vente11.setNbrLignes(2);
            vente11.setNbrPrd(2);
            vente11.setMntEncaisse(new BigDecimal("162.68"));
            enteteVenteRepository.save(vente11);

            // Sale 12 - Older sale for user2
            EnteteVente vente12 = new EnteteVente();
            vente12.setClient(user2Client);
            vente12.setDateVente(LocalDateTime.now().minusDays(18));
            vente12.setNumVente(3005L);
            vente12.setMntBrutTtc(new BigDecimal("650.25"));
            vente12.setMntNetTtc(new BigDecimal("585.23"));
            vente12.setMntRemiseTtc(new BigDecimal("65.02"));
            vente12.setTauxRemise(new BigDecimal("10.0"));
            vente12.setTotalQte(new BigDecimal("15"));
            vente12.setNbrLignes(4);
            vente12.setNbrPrd(4);
            vente12.setMntEncaisse(new BigDecimal("585.23"));
            enteteVenteRepository.save(vente12);

            // Sale 13 - Recent small sale for user2
            EnteteVente vente13 = new EnteteVente();
            vente13.setClient(user2Client);
            vente13.setDateVente(LocalDateTime.now().minusDays(25));
            vente13.setNumVente(3006L);
            vente13.setMntBrutTtc(new BigDecimal("95.50"));
            vente13.setMntNetTtc(new BigDecimal("85.95"));
            vente13.setMntRemiseTtc(new BigDecimal("9.55"));
            vente13.setTauxRemise(new BigDecimal("10.0"));
            vente13.setTotalQte(new BigDecimal("3"));
            vente13.setNbrLignes(1);
            vente13.setNbrPrd(1);
            vente13.setMntEncaisse(new BigDecimal("85.95"));
            enteteVenteRepository.save(vente13);

            // Sale 14 - Another sale for user2
            EnteteVente vente14 = new EnteteVente();
            vente14.setClient(user2Client);
            vente14.setDateVente(LocalDateTime.now().minusDays(30));
            vente14.setNumVente(3007L);
            vente14.setMntBrutTtc(new BigDecimal("310.40"));
            vente14.setMntNetTtc(new BigDecimal("279.36"));
            vente14.setMntRemiseTtc(new BigDecimal("31.04"));
            vente14.setTauxRemise(new BigDecimal("10.0"));
            vente14.setTotalQte(new BigDecimal("8"));
            vente14.setNbrLignes(3);
            vente14.setNbrPrd(3);
            vente14.setMntEncaisse(new BigDecimal("279.36"));
            enteteVenteRepository.save(vente14);
        }
    }

    private void createSampleDepots() {
        // Main depot
        Depot depot1 = new Depot();
        depot1.setCodeDepot("DEP001");
        depot1.setLibelleDepot("Dépôt Principal");
        depot1.setAdr1("Zone Industrielle Sidi Bernoussi");
        depot1.setAdr2("Casablanca");
        depot1.setCodePostal("20000");
        depot1.setPrimaire(true);
        depot1.setEstActif(true);
        depot1.setUserModifiable(true);
        depot1.setAudited(true);
        depotRepository.save(depot1);

        // Secondary depot
        Depot depot2 = new Depot();
        depot2.setCodeDepot("DEP002");
        depot2.setLibelleDepot("Dépôt Secondaire");
        depot2.setAdr1("Quartier Maarif");
        depot2.setAdr2("Casablanca");
        depot2.setCodePostal("20100");
        depot2.setPrimaire(false);
        depot2.setEstActif(true);
        depot2.setUserModifiable(true);
        depot2.setAudited(true);
        depotRepository.save(depot2);
    }

    private void createSampleOperators() {
        // Operator 1
        Operateur op1 = new Operateur();
        op1.setUsername("pharmacien1");
        op1.setFirstname("Dr. Hassan");
        op1.setLastname("Alami");
        op1.setEmail("<EMAIL>");
        op1.setPassword(passwordEncoder.encode("password"));
        op1.setActif(true);
        op1.setUserModifiable(true);
        op1.setAudited(true);
        operateurRepository.save(op1);

        // Operator 2
        Operateur op2 = new Operateur();
        op2.setUsername("assistant1");
        op2.setFirstname("Fatima");
        op2.setLastname("Benali");
        op2.setEmail("<EMAIL>");
        op2.setPassword(passwordEncoder.encode("password"));
        op2.setActif(true);
        op2.setUserModifiable(true);
        op2.setAudited(true);
        operateurRepository.save(op2);
    }

    private void createSampleSuppliers() {
        // Supplier 1
        Fournisseur fournisseur1 = new Fournisseur();
        fournisseur1.setCodeFournisseur("FOUR001");
        fournisseur1.setRaisonSociale("Laboratoires Pharmaceutiques du Maroc");
        fournisseur1.setNomCommercial("LPM");
        fournisseur1.setAdresse1("Zone Industrielle Ain Sebaa");
        fournisseur1.setVille("Casablanca");
        fournisseur1.setCodePostal("20250");
        fournisseur1.setPays("Maroc");
        fournisseur1.setTelephone("0522-123456");
        fournisseur1.setEmail("<EMAIL>");
        fournisseur1.setEstActif(true);
        fournisseur1.setEstLaboratoire(true);
        fournisseur1.setSoldeFournisseur(new BigDecimal("0.00"));
        fournisseurRepository.save(fournisseur1);

        // Supplier 2
        Fournisseur fournisseur2 = new Fournisseur();
        fournisseur2.setCodeFournisseur("FOUR002");
        fournisseur2.setRaisonSociale("Sanofi Maroc");
        fournisseur2.setNomCommercial("Sanofi");
        fournisseur2.setAdresse1("Quartier des Hôpitaux");
        fournisseur2.setVille("Casablanca");
        fournisseur2.setCodePostal("20360");
        fournisseur2.setPays("Maroc");
        fournisseur2.setTelephone("0522-789012");
        fournisseur2.setEmail("<EMAIL>");
        fournisseur2.setEstActif(true);
        fournisseur2.setEstLaboratoire(true);
        fournisseur2.setSoldeFournisseur(new BigDecimal("0.00"));
        fournisseurRepository.save(fournisseur2);
    }

    private void createSampleStock() {
        // Get sample data
        Produit produit1 = produitRepository.findByCodePrd("MED001").orElse(null);
        Produit produit2 = produitRepository.findByCodePrd("MED002").orElse(null);
        Depot depot1 = depotRepository.findByCodeDepot("DEP001").orElse(null);

        if (produit1 != null && depot1 != null) {
            // Stock for Paracetamol
            Stock stock1 = new Stock();
            stock1.setProduit(produit1);
            stock1.setDepot(depot1);
            stock1.setQteUnit(new BigDecimal("150"));
            stock1.setQteDelta(new BigDecimal("0"));
            stock1.setPrixAchatTtc(new BigDecimal("18.00"));
            stock1.setPrixVenteTtc(new BigDecimal("25.50"));
            stock1.setPrixValoTtc(new BigDecimal("21.75"));
            stock1.setNumeroLot("LOT001-2024");
            stock1.setDatePeremption(java.time.LocalDate.now().plusYears(2));
            stock1.setUserModifiable(true);
            stock1.setAudited(true);
            stockRepository.save(stock1);
        }

        if (produit2 != null && depot1 != null) {
            // Stock for Amoxicillin
            Stock stock2 = new Stock();
            stock2.setProduit(produit2);
            stock2.setDepot(depot1);
            stock2.setQteUnit(new BigDecimal("75"));
            stock2.setQteDelta(new BigDecimal("0"));
            stock2.setPrixAchatTtc(new BigDecimal("32.50"));
            stock2.setPrixVenteTtc(new BigDecimal("45.80"));
            stock2.setPrixValoTtc(new BigDecimal("39.15"));
            stock2.setNumeroLot("LOT002-2024");
            stock2.setDatePeremption(java.time.LocalDate.now().plusYears(3));
            stock2.setUserModifiable(true);
            stock2.setAudited(true);
            stockRepository.save(stock2);
        }
    }

    private void createSampleMedicalProfiles() {
        // Medical Profile for user1 (main test user)
        PersonalMedicalProfile profile1 = new PersonalMedicalProfile();
        profile1.setUsername("user1");
        profile1.setBloodType("O+");
        profile1.setAllergies("Pénicilline, Aspirine, Fruits de mer");
        profile1.setChronicConditions("Hypertension artérielle, Diabète Type 2");
        profile1.setCurrentMedications("Metformine 850mg (2x/jour), Lisinopril 10mg (1x/jour), Aspirine 75mg (1x/jour)");
        profile1.setEmergencyContact("Fatima Benali (épouse)");
        profile1.setEmergencyPhone("+212 6 87 65 43 21");
        profile1.setInsuranceNumber("CNSS-123456789");
        profile1.setDoctorName("Dr. Hassan Alami");
        profile1.setDoctorPhone("+212 5 22 34 56 78");
        profile1.setBirthDate(java.time.LocalDate.of(1975, 8, 15));
        profile1.setHeightCm(175);
        profile1.setWeightKg(78.5);
        profile1.setLastCheckupDate(java.time.LocalDate.now().minusMonths(3));
        profile1.setMedicalNotes("Patient suivi pour diabète et hypertension. Contrôle régulier nécessaire. Dernière HbA1c: 7.2%. Tension artérielle bien contrôlée.");
        personalMedicalProfileRepository.save(profile1);

        // Medical Profile for user2
        PersonalMedicalProfile profile2 = new PersonalMedicalProfile();
        profile2.setUsername("user2");
        profile2.setBloodType("A-");
        profile2.setAllergies("Aucune allergie connue");
        profile2.setChronicConditions("Asthme léger");
        profile2.setCurrentMedications("Ventoline (au besoin), Vitamine D3 1000UI (1x/jour)");
        profile2.setEmergencyContact("Ahmed Martin (frère)");
        profile2.setEmergencyPhone("+212 6 12 34 56 78");
        profile2.setInsuranceNumber("CNSS-987654321");
        profile2.setDoctorName("Dr. Aicha Bennani");
        profile2.setDoctorPhone("+212 5 22 87 65 43");
        profile2.setBirthDate(java.time.LocalDate.of(1988, 3, 22));
        profile2.setHeightCm(165);
        profile2.setWeightKg(62.0);
        profile2.setLastCheckupDate(java.time.LocalDate.now().minusMonths(6));
        profile2.setMedicalNotes("Patiente en bonne santé générale. Asthme bien contrôlé. Recommandation: activité physique régulière.");
        personalMedicalProfileRepository.save(profile2);

        // Medical Profile for testuser
        PersonalMedicalProfile profile3 = new PersonalMedicalProfile();
        profile3.setUsername("testuser");
        profile3.setBloodType("B+");
        profile3.setAllergies("Iode, Latex");
        profile3.setChronicConditions("Migraine chronique");
        profile3.setCurrentMedications("Sumatriptan 50mg (au besoin), Magnésium 300mg (1x/jour)");
        profile3.setEmergencyContact("Khadija Benali (mère)");
        profile3.setEmergencyPhone("+212 6 98 76 54 32");
        profile3.setInsuranceNumber("CNSS-456789123");
        profile3.setDoctorName("Dr. Omar Tazi");
        profile3.setDoctorPhone("+212 5 22 11 22 33");
        profile3.setBirthDate(java.time.LocalDate.of(1990, 11, 10));
        profile3.setHeightCm(180);
        profile3.setWeightKg(75.0);
        profile3.setLastCheckupDate(java.time.LocalDate.now().minusMonths(1));
        profile3.setMedicalNotes("Patient suivi pour migraines. Éviter les déclencheurs connus. Dernière consultation neurologique satisfaisante.");
        personalMedicalProfileRepository.save(profile3);

        // Medical Profile for admin
        PersonalMedicalProfile profile4 = new PersonalMedicalProfile();
        profile4.setUsername("admin");
        profile4.setBloodType("AB+");
        profile4.setAllergies("Aucune allergie connue");
        profile4.setChronicConditions("Aucune condition chronique");
        profile4.setCurrentMedications("Multivitamines (1x/jour)");
        profile4.setEmergencyContact("Service médical d'urgence");
        profile4.setEmergencyPhone("+212 5 22 00 00 00");
        profile4.setInsuranceNumber("ADMIN-000000000");
        profile4.setDoctorName("Dr. Système Admin");
        profile4.setDoctorPhone("+212 5 22 00 00 01");
        profile4.setBirthDate(java.time.LocalDate.of(1980, 1, 1));
        profile4.setHeightCm(170);
        profile4.setWeightKg(70.0);
        profile4.setLastCheckupDate(java.time.LocalDate.now().minusYears(1));
        profile4.setMedicalNotes("Profil administrateur - données de test.");
        personalMedicalProfileRepository.save(profile4);

        System.out.println("✅ Created " + personalMedicalProfileRepository.count() + " medical profiles");
    }
}


