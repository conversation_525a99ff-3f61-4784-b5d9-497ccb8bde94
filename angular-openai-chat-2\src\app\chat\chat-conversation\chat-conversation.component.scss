// chat-conversation.component.scss

:host {
  height: 100%; 
}

$primary-color: #4a6cfa;
$primary-light: #eef1ff;
$secondary-color: #f8f9fd;
$text-color: #333333;
$text-light: #6e7191;
$border-color: #e4e8f7;
$user-bubble: #4a6cfa;
$assistant-bubble: #f2f4f9;
$font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

.conversation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: $font-family;
  position: relative;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: $text-light;
    
    .welcome-message {
      text-align: center;
      max-width: 80%;
      
      h3 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
        color: $text-color;
      }
      
      p {
        font-size: 16px;
        line-height: 1.5;
      }
    }
  }
  
  .message {
    display: flex;
    gap: 12px;
    max-width: 85%;
    word-break: break-word;
    
    &.user {
      align-self: flex-end;
      flex-direction: row-reverse;
      
      .avatar {
        background-color: $user-bubble;
        color: white;
      }
      
      .content {
        background-color: $user-bubble;
        color: white;
        border-radius: 18px 18px 4px 18px;
        
        p {
          a {
            color: white;
            text-decoration: underline;
          }
        }
      }
    }
    
    &.assistant {
      align-self: flex-start;
      
      .avatar {
        background-color: $primary-light;
        color: $primary-color;
      }
      
      .content {
        background-color: $assistant-bubble;
        color: $text-color;
        border-radius: 18px 18px 18px 4px;
      }
    }
    
    .avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      flex-shrink: 0;
    }
    
    .content {
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.5;
      
      p {
        margin: 0;
        white-space: pre-wrap;
        
        strong {
          font-weight: 600;
        }
        
        em {
          font-style: italic;
        }
        
        a {
          color: $primary-color;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
        
        code {
          background-color: rgba(0, 0, 0, 0.05);
          padding: 2px 4px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
        }
      }
    }
  }
  
  .typing-indicator {
    display: flex;
    gap: 4px;
    padding: 8px 0;
    
    span {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: $text-light;
      opacity: 0.6;
      animation: typing 1.4s infinite both;
      
      &:nth-child(2) {
        animation-delay: 0.2s;
      }
      
      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }
}

@keyframes typing {
  0% {
    opacity: 0.6;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-4px);
  }
  100% {
    opacity: 0.6;
    transform: translateY(0);
  }
}

.input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid $border-color;
  background-color: white;
  position: relative;
  z-index: 10;
  position: sticky;
  bottom: 0;
  
  .message-input {
    flex: 1;
    border: 1px solid $border-color;
    border-radius: 24px;
    padding: 12px 16px;
    font-family: inherit;
    font-size: 14px;
    line-height: 24px; // Consistent line height for proper calculation
    resize: none;
    min-height: 54px; // Minimum height for one line
    max-height: 144px; // Maximum height for 6 lines (24px * 6)
    height: 54px; // Initial height
    overflow-y: hidden; // Hide scrollbar initially
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    box-sizing: border-box;

    &:focus {
      border-color: $primary-color;
      box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
    }

    &::placeholder {
      color: $text-light;
    }

    &:disabled {
      background-color: $secondary-color;
      cursor: not-allowed;
    }

    // Smooth scrollbar when content exceeds max height
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }
  }
  
  .send-button {
    background-color: $primary-color;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    
    &:hover {
      background-color: darken($primary-color, 5%);
    }
    
    &:disabled {
      background-color: lighten($primary-color, 20%);
      cursor: not-allowed;
    }
    
    .send-icon {
      font-size: 14px;
      // transform: rotate(90deg);
    }
  }
}
