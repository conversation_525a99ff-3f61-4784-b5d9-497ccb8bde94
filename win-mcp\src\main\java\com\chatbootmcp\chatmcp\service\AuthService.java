package com.chatbootmcp.chatmcp.service;

import com.chatbootmcp.chatmcp.dto.request.LoginRequest;
import com.chatbootmcp.chatmcp.dto.response.AuthResponse;
import com.chatbootmcp.chatmcp.entity.User;
import com.chatbootmcp.chatmcp.repository.UserRepository;
import com.chatbootmcp.chatmcp.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtUtil jwtUtil;

    public AuthResponse login(LoginRequest loginRequest) {
        System.out.println("AuthService: Login attempt for user: " + loginRequest.getUsername());

        try {
            // Check if user exists in the database
            boolean userExists = userRepository.existsByUsername(loginRequest.getUsername());
            System.out.println("AuthService: User exists in database: " + userExists);

            if (userExists) {
                User user = userRepository.findByUsername(loginRequest.getUsername()).get();
                System.out.println("AuthService: Found user: " + user.getUsername() + ", ID: " + user.getId());
            }

            // Attempt authentication
            System.out.println("AuthService: Attempting authentication...");
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getUsername(),
                            loginRequest.getPassword()
                    )
            );

            System.out.println("AuthService: Authentication successful");
            SecurityContextHolder.getContext().setAuthentication(authentication);
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();

            // Generate JWT token
            System.out.println("AuthService: Generating JWT token...");
            String jwt = jwtUtil.generateToken(userDetails);

            User user = userRepository.findByUsername(userDetails.getUsername())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            System.out.println("AuthService: Login successful for user: " + user.getUsername());
            return new AuthResponse(jwt, user.getUsername(), user.getId(), user.getEmail());
        } catch (Exception e) {
            System.out.println("AuthService: Login failed - " + e.getClass().getName() + ": " + e.getMessage());
            throw e;
        }
    }

    /**
     * Tenant authentication for pharmacy/tenant login
     * This method handles the first step of dual authentication
     */
    public AuthResponse tenantLogin(LoginRequest loginRequest) {
        System.out.println("🏥 AuthService: Tenant login attempt for: " + loginRequest.getUsername());

        try {
            // For demo purposes, we'll create a special tenant user if it doesn't exist
            String tenantUsername = loginRequest.getUsername();

            // Check if tenant user exists, if not create it
            if (!userRepository.existsByUsername(tenantUsername)) {
                System.out.println("🏥 AuthService: Creating tenant user: " + tenantUsername);
                User tenantUser = new User();
                tenantUser.setUsername(tenantUsername);
                tenantUser.setPassword(passwordEncoder.encode(loginRequest.getPassword()));
                tenantUser.setEmail(tenantUsername + "@pharmacy.com");
                tenantUser.setFullName("Pharmacy Tenant");
                userRepository.save(tenantUser);
                System.out.println("🏥 AuthService: Tenant user created successfully");
            }

            // Authenticate the tenant
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getUsername(),
                            loginRequest.getPassword()
                    )
            );

            System.out.println("🏥 AuthService: Tenant authentication successful");
            SecurityContextHolder.getContext().setAuthentication(authentication);
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();

            // Generate JWT token for tenant
            String jwt = jwtUtil.generateToken(userDetails);

            User tenantUser = userRepository.findByUsername(userDetails.getUsername())
                    .orElseThrow(() -> new RuntimeException("Tenant user not found"));

            System.out.println("🏥 AuthService: Tenant login successful for: " + tenantUser.getUsername());
            return new AuthResponse(jwt, tenantUser.getUsername(), tenantUser.getId(), tenantUser.getEmail());
        } catch (Exception e) {
            System.out.println("❌ AuthService: Tenant login failed - " + e.getClass().getName() + ": " + e.getMessage());
            throw e;
        }
    }
}
